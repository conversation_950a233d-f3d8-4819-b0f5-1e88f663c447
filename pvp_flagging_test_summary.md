# PvP Flagging Fix Summary

## Vấn đề đã khắc phục

### Vấn đề 1: Servitor Retaliation
- **Tình huống**: Player A tấn công Summoner (Player B) → Servitor phản công và giết Player A → Player B bị PK status
- **<PERSON><PERSON><PERSON><PERSON> nhân**: <PERSON><PERSON> thống không phân biệt được defensive action
- **Giải pháp**: Track aggressor và sử dụng defensive attack

### Vấn đề 2: Reflect Damage  
- **Tình huống**: Player A tấn công Summon → Su<PERSON><PERSON> có reflect damage → Player A chết do reflect damage → Player B bị PK status
- **Nguyên nhân**: Reflect damage được coi như normal attack từ owner
- **G<PERSON><PERSON><PERSON> pháp**: Track reflect damage kills và coi như defensive

## Code Changes

### 1. Player.java
- Added `_aggressors` map để track kẻ tấn công
- Added `_reflectDamageKills` set để track reflect damage kills
- Updated `updatePvPStatus()` để mark aggressors
- Updated `onPlayerKill()` để check defensive kills
- Added aggressor management methods
- Override `doAttack()` để track reflect damage

### 2. Summon.java  
- Added `_isDefensiveAttack` flag
- Added `_reflectDamageTargets` set
- Added `doDefensiveAttack()` method
- Override `doAttack()` để track reflect damage từ summon
- Added defensive attack management methods

### 3. SummonAI.java
- Updated `onEvtAttacked()` để mark attacker as aggressor against owner
- Updated `defendAttack()` để use `doDefensiveAttack()`

### 4. Creature.java
- Updated attack logic để check defensive attacks
- Prevent PvP flagging cho defensive attacks

## Logic Flow

### Defensive Retaliation:
1. Player A attacks Player B → `Player B.addAggressor(Player A.getObjectId())`
2. Servitor defends → `SummonAI.defendAttack()` → `Summon.doDefensiveAttack()`
3. Servitor attacks Player A → `Creature.doAttack()` detects defensive → no PvP flagging
4. Player A dies → `Player.onPlayerKill()` checks `wasAggressor()` → PvP kill, not PK

### Reflect Damage:
1. Player A attacks Summon → `Owner.addAggressor(Player A.getObjectId())`
2. Reflect damage occurs → `Summon.doAttack()` with `reflect=true`
3. Track reflect damage target → `_reflectDamageKills.add(Player A.getObjectId())`
4. Player A dies → `Player.onPlayerKill()` checks `isReflectDamageKill()` → PvP kill, not PK

## Expected Results
- Summoners không bị PK status khi servitor tự vệ
- Summoners không bị PK status khi reflect damage giết kẻ thù
- Kẻ tấn công ban đầu vẫn bị PvP flag đúng cách
- Defensive actions được xử lý như PvP kills, không phải PKs
- Aggressor tracking tự động expire sau 5 phút
- Reflect damage tracking tự động expire sau 5 giây

## Test Cases
1. **Normal PvP**: Player A attacks Player B → Player B kills Player A → Player B gets PK (unchanged)
2. **Defensive Retaliation**: Player A attacks Player B → Servitor kills Player A → Player B gets PvP kill
3. **Reflect Damage**: Player A attacks Summon → Reflect damage kills Player A → Player B gets PvP kill
4. **Mixed Scenario**: Player A attacks both Player B and Summon → Any defensive kill should be PvP, not PK
