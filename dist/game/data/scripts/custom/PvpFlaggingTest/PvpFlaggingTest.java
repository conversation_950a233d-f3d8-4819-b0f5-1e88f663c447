/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package custom.PvpFlaggingTest;

import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.Summon;
import org.l2jmobius.gameserver.model.events.EventType;
import org.l2jmobius.gameserver.model.events.ListenerRegisterType;
import org.l2jmobius.gameserver.model.events.annotations.RegisterEvent;
import org.l2jmobius.gameserver.model.events.annotations.RegisterType;
import org.l2jmobius.gameserver.model.events.impl.creature.OnCreatureDeath;
import org.l2jmobius.gameserver.model.quest.Quest;
import org.l2jmobius.gameserver.model.quest.QuestState;

/**
 * Test script for PvP flagging fix for Summoner servitor defense
 * <AUTHOR>
 */
public class PvpFlaggingTest extends Quest
{
	public PvpFlaggingTest()
	{
		super(-1, PvpFlaggingTest.class.getSimpleName(), "Test Scripts");
	}
	
	@Override
	public String onAdvEvent(String event, Player player, Player npc)
	{
		if (event.equals("test_pvp_flagging"))
		{
			return testPvpFlagging(player);
		}
		return null;
	}
	
	/**
	 * Test the PvP flagging logic for summoner defense
	 */
	private String testPvpFlagging(Player player)
	{
		final StringBuilder result = new StringBuilder();
		result.append("<html><body>");
		result.append("<title>PvP Flagging Test Results</title>");
		result.append("<br><br>");
		
		// Test aggressor tracking
		result.append("<font color=\"LEVEL\">Testing Aggressor Tracking:</font><br>");
		
		// Simulate adding an aggressor
		player.addAggressor(12345);
		if (player.wasAggressor(12345))
		{
			result.append("<font color=\"00FF00\">✓ addAggressor() and wasAggressor() working correctly</font><br>");
		}
		else
		{
			result.append("<font color=\"FF0000\">✗ addAggressor() or wasAggressor() not working</font><br>");
		}
		
		// Test aggressor removal
		player.removeAggressor(12345);
		if (!player.wasAggressor(12345))
		{
			result.append("<font color=\"00FF00\">✓ removeAggressor() working correctly</font><br>");
		}
		else
		{
			result.append("<font color=\"FF0000\">✗ removeAggressor() not working</font><br>");
		}
		
		// Test servitor defensive attack
		result.append("<br><font color=\"LEVEL\">Testing Servitor Defensive Attack:</font><br>");
		if (player.hasServitors())
		{
			for (Summon servitor : player.getServitors().values())
			{
				// Test defensive attack flag
				servitor.doDefensiveAttack(player); // Test with dummy target
				if (servitor.isDefensiveAttack())
				{
					result.append("<font color=\"00FF00\">✓ doDefensiveAttack() sets defensive flag correctly</font><br>");
					servitor.resetDefensiveAttack();
					if (!servitor.isDefensiveAttack())
					{
						result.append("<font color=\"00FF00\">✓ resetDefensiveAttack() working correctly</font><br>");
					}
					else
					{
						result.append("<font color=\"FF0000\">✗ resetDefensiveAttack() not working</font><br>");
					}
				}
				else
				{
					result.append("<font color=\"FF0000\">✗ doDefensiveAttack() not setting defensive flag</font><br>");
				}
				break; // Test only first servitor
			}
		}
		else
		{
			result.append("<font color=\"FFFF00\">! No servitors found to test</font><br>");
		}
		
		// Test PK/PvP status
		result.append("<br><font color=\"LEVEL\">Current Player Status:</font><br>");
		result.append("PvP Kills: " + player.getPvpKills() + "<br>");
		result.append("PK Kills: " + player.getPkKills() + "<br>");
		result.append("Reputation: " + player.getReputation() + "<br>");
		result.append("PvP Flag: " + player.getPvpFlag() + "<br>");
		
		result.append("<br><font color=\"LEVEL\">Test Instructions:</font><br>");
		result.append("1. Have another player attack you<br>");
		result.append("2. Let your servitor kill the attacker<br>");
		result.append("3. Check that you don't get PK status<br>");
		result.append("4. You should get PvP kill instead<br>");
		
		result.append("<br><button value=\"Run Test Again\" action=\"bypass -h Quest PvpFlaggingTest test_pvp_flagging\" width=150 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		result.append("</body></html>");
		
		return result.toString();
	}
	
	@RegisterEvent(EventType.ON_CREATURE_DEATH)
	@RegisterType(ListenerRegisterType.GLOBAL)
	public void onCreatureDeath(OnCreatureDeath event)
	{
		if (event.getTarget().isPlayer() && event.getAttacker().isPlayer())
		{
			final Player killed = event.getTarget().getActingPlayer();
			final Player killer = event.getAttacker().getActingPlayer();
			
			// Log the death for testing purposes
			if (killed.wasAggressor(killer.getObjectId()))
			{
				killer.sendMessage("DEBUG: " + killed.getName() + " was your aggressor - this should be PvP kill, not PK");
			}
			else
			{
				killer.sendMessage("DEBUG: " + killed.getName() + " was not your aggressor - this might be PK");
			}
		}
		else if (event.getTarget().isPlayer() && event.getAttacker().isSummon())
		{
			final Player killed = event.getTarget().getActingPlayer();
			final Summon killerSummon = (Summon) event.getAttacker();
			final Player owner = killerSummon.getOwner();
			
			if (owner != null)
			{
				// Log servitor kill for testing
				if (killed.wasAggressor(owner.getObjectId()))
				{
					owner.sendMessage("DEBUG: Your servitor killed " + killed.getName() + " who was your aggressor - this should be PvP kill, not PK");
				}
				else
				{
					owner.sendMessage("DEBUG: Your servitor killed " + killed.getName() + " who was not your aggressor - this might be PK");
				}
			}
		}
	}
	
	public static void main(String[] args)
	{
		new PvpFlaggingTest();
	}
}
