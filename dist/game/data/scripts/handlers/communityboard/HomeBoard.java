/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.communityboard;

import java.security.MessageDigest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.concurrent.TimeUnit;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import java.util.logging.Logger;

import org.l2jmobius.Config;
import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.commons.util.Rnd;
import org.l2jmobius.commons.util.TimeUtil;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.communitybbs.Manager.PartyMatchingBBSManager;
import org.l2jmobius.gameserver.communitybbs.Manager.PlayerScheme;
import org.l2jmobius.gameserver.communitybbs.Manager.SchemeBuff;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.PartyMatchingBoard;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoFunctions;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoHandler;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoHolder;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.ImagesCache;
import org.l2jmobius.gameserver.data.sql.ClanTable;
import org.l2jmobius.gameserver.data.xml.ClassListData;
import org.l2jmobius.gameserver.data.xml.DynamicExpRateData;
import org.l2jmobius.gameserver.data.xml.ItemData;
import org.l2jmobius.gameserver.data.xml.NpcData;
import org.l2jmobius.gameserver.data.xml.SkillData;
import org.l2jmobius.gameserver.data.xml.SpawnData;
import org.l2jmobius.gameserver.enums.ChatType;
import org.l2jmobius.gameserver.enums.DropType;
import org.l2jmobius.gameserver.enums.PartyDistributionType;
import org.l2jmobius.gameserver.enums.PlayerAction;
import org.l2jmobius.gameserver.enums.PlayerCondOverride;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.handler.IParseBoardHandler;
import org.l2jmobius.gameserver.instancemanager.CollectionManager;
import org.l2jmobius.gameserver.instancemanager.GiftCodeManager;
import org.l2jmobius.gameserver.instancemanager.GrandBossManager;
import org.l2jmobius.gameserver.instancemanager.PcCafePointsManager;
import org.l2jmobius.gameserver.instancemanager.PremiumManager;
import org.l2jmobius.gameserver.instancemanager.ReferralManager;
import org.l2jmobius.gameserver.model.BlockList;
import org.l2jmobius.gameserver.model.Location;
import org.l2jmobius.gameserver.model.Party;
import org.l2jmobius.gameserver.model.StatSet;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Playable;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.Summon;
import org.l2jmobius.gameserver.model.actor.request.PartyRequest;
import org.l2jmobius.gameserver.model.actor.templates.NpcTemplate;
import org.l2jmobius.gameserver.model.cubic.Cubic;
import org.l2jmobius.gameserver.model.holders.DropGroupHolder;
import org.l2jmobius.gameserver.model.holders.DropHolder;
import org.l2jmobius.gameserver.model.item.ItemTemplate;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.model.item.type.WeaponType;
import org.l2jmobius.gameserver.model.itemcontainer.Inventory;
import org.l2jmobius.gameserver.model.olympiad.OlympiadManager;
import org.l2jmobius.gameserver.model.skill.Skill;
import org.l2jmobius.gameserver.model.spawns.NpcSpawnTemplate;
import org.l2jmobius.gameserver.model.stats.BaseStat;
import org.l2jmobius.gameserver.model.stats.Stat;
import org.l2jmobius.gameserver.model.stats.TraitType;
import org.l2jmobius.gameserver.model.variables.PlayerVariables;
import org.l2jmobius.gameserver.model.vip.VipManager;
import org.l2jmobius.gameserver.model.zone.ZoneId;
import org.l2jmobius.gameserver.network.SystemMessageId;
import org.l2jmobius.gameserver.network.serverpackets.ActionFailed;
import org.l2jmobius.gameserver.network.serverpackets.AskJoinParty;
import org.l2jmobius.gameserver.network.serverpackets.ConfirmDlg;
import org.l2jmobius.gameserver.network.serverpackets.CreatureSay;
import org.l2jmobius.gameserver.network.serverpackets.ExShowScreenMessage;
import org.l2jmobius.gameserver.network.serverpackets.MagicSkillUse;
import org.l2jmobius.gameserver.network.serverpackets.NpcHtmlMessage;
import org.l2jmobius.gameserver.network.serverpackets.RadarControl;
import org.l2jmobius.gameserver.network.serverpackets.SystemMessage;
import org.l2jmobius.gameserver.taskmanager.AutoPlayTaskManager;
import org.l2jmobius.gameserver.taskmanager.GameTimeTaskManager;
import org.l2jmobius.gameserver.util.Util;

/**
 * Home board.
 * 
 * <AUTHOR> Mobius
 */
public class HomeBoard implements IParseBoardHandler
{
	private static final Logger							LOGGER					= Logger.getLogger(HomeBoard.class.getName());
	private static int									LCOIN					= 91663;
	private static final boolean						DEBUG					= false;
	private static final String							TITLE_NAME				= "Scheme Buffer";
	private static final boolean						ENABLE_SCHEME_SYSTEM	= Config.NpcBuffer_EnableScheme;
	private static final boolean						ENABLE_HEAL				= Config.NpcBuffer_EnableHeal;
	private static final boolean						ENABLE_BUFFS			= Config.NpcBuffer_EnableBuffs;
	private static final boolean						ENABLE_RESIST			= Config.NpcBuffer_EnableResist;
	private static final boolean						ENABLE_SONGS			= Config.NpcBuffer_EnableSong;
	private static final boolean						ENABLE_DANCES			= Config.NpcBuffer_EnableDance;
	private static final boolean						ENABLE_CHANTS			= Config.NpcBuffer_EnableChant;
	private static final boolean						ENABLE_OTHERS			= Config.NpcBuffer_EnableOther;
	private static final boolean						ENABLE_SPECIAL			= Config.NpcBuffer_EnableSpecial;
	private static final boolean						ENABLE_CUBIC			= Config.NpcBuffer_EnableCubic;
	private static final boolean						ENABLE_BUFF_REMOVE		= Config.NpcBuffer_EnableCancel;
	private static final boolean						ENABLE_BUFF_SET			= Config.NpcBuffer_EnableBuffSet;
	private static final boolean						BUFF_WITH_KARMA			= Config.NpcBuffer_EnableBuffPK;
	private static final boolean						FREE_BUFFS				= Config.NpcBuffer_EnableFreeBuffs;
	private static final int							MIN_LEVEL				= Config.NpcBuffer_MinLevel;
	private static final int							BUFF_REMOVE_PRICE		= Config.NpcBuffer_PriceCancel;
	private static final int							HEAL_PRICE				= Config.NpcBuffer_PriceHeal;
	private static final int							BUFF_PRICE				= Config.NpcBuffer_PriceBuffs;
	private static final int							RESIST_PRICE			= Config.NpcBuffer_PriceResist;
	private static final int							SONG_PRICE				= Config.NpcBuffer_PriceSong;
	private static final int							DANCE_PRICE				= Config.NpcBuffer_PriceDance;
	private static final int							CHANT_PRICE				= Config.NpcBuffer_PriceChant;
	private static final int							OTHERS_PRICE			= Config.NpcBuffer_PriceOther;
	private static final int							SPECIAL_PRICE			= Config.NpcBuffer_PriceSpecial;
	private static final int							CUBIC_PRICE				= Config.NpcBuffer_PriceCubic;
	private static final int							BUFF_SET_PRICE			= Config.NpcBuffer_PriceSet;
	private static final int							SCHEME_BUFF_PRICE		= Config.NpcBuffer_PriceScheme;
	private static final int							SCHEMES_PER_PLAYER		= Config.NpcBuffer_MaxScheme;
	private static final int							MAX_SCHEME_BUFFS		= Config.BUFFS_MAX_AMOUNT;
	private static final int							MAX_SCHEME_DANCES		= Config.DANCES_MAX_AMOUNT;
	private static final int							CONSUMABLE_ID			= 57;
	private static final int							CONSUMABLE_LCOIN_ID		= 91663;
	private static final String							SET_FIGHTER				= "Fighter";
	private static final String							SET_MAGE				= "Mage";
	private static final String							SET_ALL					= "All";
	private static final String							SET_NONE				= "None";
	private static final String[]						SCHEME_ICONS			= new String[]
	{
		"Icon.skill1331",
		"Icon.skill1332",
		"Icon.skill1316",
		"Icon.skill1264",
		"Icon.skill1254",
		"Icon.skill1178",
		"Icon.skill1085",
		"Icon.skill957",
		"Icon.skill0928",
		"Icon.skill0793",
		"Icon.skill0787",
		"Icon.skill0490",
		"Icon.skill0487",
		"Icon.skill0452",
		"Icon.skill0453",
		"Icon.skill0440",
		"Icon.skill0409",
		"Icon.skill0405",
		"Icon.skill0061",
		"Icon.skill0072",
		"Icon.skill0219",
		"Icon.skill0208",
		"Icon.skill0210",
		"Icon.skill0254",
		"Icon.skill0228",
		"Icon.skill0222",
		"Icon.skill0181",
		"Icon.skill0078",
		"Icon.skill0091",
		"Icon.skill0076",
		"Icon.skill0025",
		"Icon.skill0018",
		"Icon.skill0019",
		"Icon.skill0007",
		"Icon.skill1391",
		"Icon.skill1373",
		"Icon.skill1388",
		"Icon.skill1409",
		"Icon.skill1457",
		"Icon.skill1501",
		"Icon.skill1520",
		"Icon.skill1506",
		"Icon.skill1527",
		"Icon.skill5016",
		"Icon.skill5860",
		"Icon.skill5661",
		"Icon.skill6302",
		"Icon.skill6171",
		"Icon.skill6286",
		"Icon.skill4106",
		"Icon.skill4270_3"
	};
	private static boolean								singleBuffsLoaded		= false;
	private static List<SingleBuff>						allSingleBuffs			= null;
	// SQL Queries
	private static final String							COUNT_FAVORITES			= "SELECT COUNT(*) AS favorites FROM `bbs_favorites` WHERE `playerId`=?";
	private static final String[]						COMMANDS				=
	{
		"_bbshome",
		"_bbstop",
	};
	private static final String[]						CUSTOM_COMMANDS			=
	{
		Config.PREMIUM_SYSTEM_ENABLED && Config.COMMUNITY_PREMIUM_SYSTEM_ENABLED ? "_bbspremium" : null,
		Config.COMMUNITYBOARD_ENABLE_MULTISELLS ? "_bbsexcmultisell" : null,
		Config.COMMUNITYBOARD_ENABLE_MULTISELLS ? "_bbsmultisell" : null,
		Config.COMMUNITYBOARD_ENABLE_MULTISELLS ? "_bbssell" : null,
		Config.COMMUNITYBOARD_ENABLE_TELEPORTS ? "_bbsteleport" : null,
		Config.COMMUNITYBOARD_ENABLE_BUFFS ? "_bbsbuff" : null,
		Config.COMMUNITYBOARD_ENABLE_HEAL ? "_bbsheal" : null,
		Config.COMMUNITYBOARD_ENABLE_DELEVEL ? "_bbsdelevel" : null,
		"changeIcon",
		"changeIcon_1",
		"changeName",
		"changeName_1",
		"giveBuffSet",
		"manage_scheme_select",
		"delete_1",
		"edit_1",
		"create_1",
		"delete_c",
		"delete",
		"create",
		"add_buff",
		"remove_buff",
		"manage_scheme_1",
		"cast",
		"removeBuffs",
		"heal",
		"castBuffSet",
		"giveBuffs",
		"editSelectedBuff",
		"changeBuffSet",
		"edit_buff_list",
		"buffpet",
		"redirect",
		"toggle_payment_mode",
		// Change Pass
		"_bbschangepass",
		"_bbschangepass_action ",
		"_bbssetsecret_action ",
		"_bbschangesecret",
		"_bbssuccess_secret",
		"_bbschangesecret_action ",
		"_bbsconfirmsecret_action ",
		"_bbsofflineFarm",
		"_bbsreferral",
		"_bbsreferral_enter",
		"_bbsreferral_referred_rewards",
		"_bbsreferral_referrer_rewards",
		"_bbsreferral_view_activity ",
		"_bbsreferral_claim_reward",
		"_bbsreferral_claim_rewards ",
		"_bbscustom_referral_home",
		"_bbsreferral_cancel_referral",
		"_bbshowCollection",
		"_bbshowCategory",
		"_bbshowCollectionDetails",
		"_bbcollectItem",
		"_bbsbuyCollection",
		"_bbadminViewPlayerCollections",
		"_bbadminDeleteCollection",
		"_bbadminPlayerManagement",
		"_bbadminAddCollection",
		"_bbssearchdropCalc",
		"_bbssearchdropItemsByName_",
		"_bbssearchdropMonstersByItem_",
		"_bbssearchdropMonsterDetailsByItem_",
		"_bbssearchdropMonstersByName_",
		"_bbssearchdropMonsterDetailsByName_",
		"_bbssearchNpcDropList",
		"_bbssearchShowSkills",
		"_bbspartymatchinginvite",
		"_bbspartymatchinglist",
		"_bbspartymatchingrefresh",
		"_bbspartymatching",
		"_bbsstats",
		"_bbsgrandbossstatus",
		"_bbstop_settings_menu_autoplayer",
		"_bbstop_settings_menu_autoplayer_townmode",
		"_bbstop_settings_menu_autoplayer_2",
		"_bbstop_settings_menu_autoplayer_3",
		"_bbstop_settings_menu_autoplayer_4",
		"_bbstop_settings_menu_set_hp_threshold",
		"_bbstop_settings_menu_autoplayer_5",
		"_bbstop_settings_menu_set_mp_threshold",
		"_bbstop_settings_menu_autoplayer_6"
	};
	private static final BiPredicate<String, Player>	COMBAT_CHECK			= (command, player) ->
																				{
																					boolean commandCheck = false;
																					for (String c : CUSTOM_COMMANDS)
																					{
																						if ((c != null) && command.startsWith(c))
																						{
																							commandCheck = true;
																							break;
																						}
																					}
																					return commandCheck && (player.isCastingNow() || player.isInCombat() || player.isInDuel() || player.isInOlympiadMode() || player.isInsideZone(ZoneId.SIEGE) || player.isInsideZone(ZoneId.PVP) || (player.getPvpFlag() > 0) || player.isAlikeDead() || player.isOnEvent());
																				};
	private static final Predicate<Player>				KARMA_CHECK				= player -> Config.COMMUNITYBOARD_KARMA_DISABLED && (player.getReputation() < 0);
	
	@Override
	public String[] getCommunityBoardCommands()
	{
		final List<String> commands = new ArrayList<>();
		commands.addAll(Arrays.asList(COMMANDS));
		commands.addAll(Arrays.asList(CUSTOM_COMMANDS));
		return commands.stream().filter(Objects::nonNull).toArray(String[]::new);
	}
	
	@Override
	public boolean parseCommunityBoardCommand(String command, Player player)
	{
		// Old custom conditions check move to here
		if (COMBAT_CHECK.test(command, player) && !player.isGM())
		{
			player.sendMessage("You can't use the Community Board right now.");
			return false;
		}
		if (KARMA_CHECK.test(player))
		{
			player.sendMessage("Players with Karma cannot use the Community Board.");
			return false;
		}
		String returnHtml = null;
		// final String navigation = HtmCache.getInstance().getHtm(player, NAVIGATION_PATH);
		if (command.equals("_bbshome") || command.equals("_bbstop"))
		{
			final String customPath = Config.CUSTOM_CB_ENABLED ? "Custom/" : "";
			CommunityBoardHandler.getInstance().addBypass(player, "Home", command);
			returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/" + customPath + "home.html");
			returnHtml = replaceVars(player, returnHtml);
			if (!Config.CUSTOM_CB_ENABLED)
			{
				returnHtml = returnHtml.replace("%fav_count%", Integer.toString(getFavoriteCount(player)));
				returnHtml = returnHtml.replace("%region_count%", Integer.toString(getRegionCount(player)));
				returnHtml = returnHtml.replace("%clan_count%", Integer.toString(ClanTable.getInstance().getClanCount()));
			}
		}
		else if (command.startsWith("_bbstop;"))
		{
			final String customPath = Config.CUSTOM_CB_ENABLED ? "Custom/" : "";
			final String path = command.replace("_bbstop;", "");
			if ((path.length() > 0) && path.endsWith(".html"))
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/" + customPath + path);
				returnHtml = replaceVars(player, returnHtml);
			}
		}
		else if (command.startsWith("_bbspremium"))
		{
			final String fullBypass = command.replace("_bbspremium;", "");
			final String[] buypassOptions = fullBypass.split(",");
			final int premiumDays = Integer.parseInt(buypassOptions[0]);
			if ((premiumDays < 1) || (premiumDays > 30) || (player.getInventory().getInventoryItemCount(Config.COMMUNITY_PREMIUM_COIN_ID, -1) < (Config.COMMUNITY_PREMIUM_PRICE_PER_DAY * premiumDays)))
			{
				player.sendMessage("Not enough currency!");
			}
			else
			{
				player.destroyItemByItemId("CB_Premium", Config.COMMUNITY_PREMIUM_COIN_ID, Config.COMMUNITY_PREMIUM_PRICE_PER_DAY * premiumDays, player, true);
				PremiumManager.getInstance().addPremiumTime(player.getAccountName(), premiumDays, TimeUnit.DAYS);
				player.sendMessage("Your account will now have premium status until " + new SimpleDateFormat("dd.MM.yyyy HH:mm").format(PremiumManager.getInstance().getPremiumExpiration(player.getAccountName())) + ".");
				if (Config.PC_CAFE_RETAIL_LIKE)
				{
					PcCafePointsManager.getInstance().run(player);
				}
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/premium/thankyou.html");
			}
		}
		else if (command.startsWith("_bbshome giftcode"))
		{
			returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/giftcode.html");
			returnHtml = replaceVars(player, returnHtml);
		}
		else if (command.startsWith("_bbshome usegiftcode"))
		{
			final String[] parts = command.split(" ");
			if (parts.length >= 3)
			{
				final String giftCode = parts[2];
				final String result = GiftCodeManager.getInstance().useGiftCode(player, giftCode);
				player.sendMessage(result);
			}
			else
			{
				player.sendMessage("Vui lòng nhập mã gift code!");
			}
			returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/giftcode.html");
			returnHtml = replaceVars(player, returnHtml);
		}
		if (command.startsWith("_bbsbufferbypass"))
		{
			StringTokenizer st = new StringTokenizer(command, " ");
			st.nextToken();
			if (!st.hasMoreTokens())
			{
				return false;
			}
			String next = st.nextToken();
			if (next.startsWith("menu"))
			{
				CommunityBoardHandler.separateAndSend(main(player), player);
			}
			else
			{
				String msg = null;
				String[] eventSplit = command.split(" ");
				if (eventSplit.length < 4)
				{
					return false;
				}
				// If buffs were not loaded, load them now
				if (!singleBuffsLoaded)
				{
					singleBuffsLoaded = true;
					loadSingleBuffs();
				}
				@SuppressWarnings("unused")
				String eventParam0 = eventSplit[0];
				String eventParam1 = eventSplit[1];
				String eventParam2 = eventSplit[2];
				String eventParam3 = eventSplit[3];
				String eventParam4 = eventSplit[4];
				if (!eventParam1.equals("heal") && !canHeal(player) /* && !player.getVariables().getBoolean("BackHpOn") */)
				{
					player.getVariables().set("BackHpOn", true);
					Playable target = isPetBuff(player) ? player.getPet() : player;
					if (!isPetBuff(player))
					{
						ThreadPool.schedule(new BackHp(target, target.getCurrentHp(), target.getCurrentMp(), target.getCurrentCp()), 250);
					}
					if (player.getPet() != null)
					{
						ThreadPool.schedule(new BackHp(player.getPet(), target.getCurrentHp(), target.getCurrentMp(), target.getCurrentCp()), 250);
					}
				}
				// if (!FREE_BUFFS)
				// {
				// if (player.getLCoin() < SCHEME_BUFF_PRICE)
				// {
				// sendErrorMessageToPlayer(player, "You do not have enough L-Coin. You need at least " + SCHEME_BUFF_PRICE + " Adena.");
				// CommunityBoardHandler.separateAndSend(main(player), player);
				// return false;
				// }
				// }
				if (eventParam1.equalsIgnoreCase("buffpet"))
				{
					setPetBuff(player, eventParam2);
					msg = main(player);
				}
				else if (eventParam1.equals("redirect"))
				{
					if (eventParam2.equals("main"))
					{
						msg = main(player);
					}
					else if (eventParam2.equals("manage_buffs"))
					{
						msg = viewAllBuffTypes();
					}
					else if (eventParam2.equals("view_buffs"))
					{
						msg = buildHtml("buff", player);
					}
					else if (eventParam2.equals("view_resists"))
					{
						msg = buildHtml("resist", player);
					}
					else if (eventParam2.equals("view_songs"))
					{
						msg = buildHtml("song", player);
					}
					else if (eventParam2.equals("view_dances"))
					{
						msg = buildHtml("dance", player);
					}
					else if (eventParam2.equals("view_chants"))
					{
						msg = buildHtml("chant", player);
					}
					else if (eventParam2.equals("view_others"))
					{
						msg = buildHtml("others", player);
					}
					else if (eventParam2.equals("view_special"))
					{
						msg = buildHtml("special", player);
					}
					else if (eventParam2.equals("view_cubic"))
					{
						msg = buildHtml("cubic", player);
					}
					else if (DEBUG)
					{
						throw new RuntimeException();
					}
				}
				else if (eventParam1.equalsIgnoreCase("toggle_payment_mode"))
				{
					// Thay đổi trạng thái thanh toán
					player.setUsingLcoins(!player.isUsingLcoins());
					// Gửi lại trang chính để cập nhật màu sắc
					CommunityBoardHandler.separateAndSend(main(player), player);
				}
				else if (eventParam1.equalsIgnoreCase("edit_buff_list"))
				{
					msg = viewAllBuffs(eventParam2, eventParam3, eventParam4);
				}
				else if (eventParam1.equalsIgnoreCase("changeBuffSet"))
				{
					final int skillId = Integer.parseInt(eventParam2);
					final int skillLevel = Integer.parseInt(eventParam3);
					final String page = eventSplit[5];
					final String type = eventSplit[6];
					int forClass = 0;
					if (eventParam4.equals(SET_FIGHTER))
					{
						forClass = 0;
					}
					else if (eventParam4.equals(SET_MAGE))
					{
						forClass = 1;
					}
					else if (eventParam4.equals(SET_ALL))
					{
						forClass = 2;
					}
					else if (eventParam4.equals(SET_NONE))
					{
						forClass = 3;
					}
					else if (DEBUG)
					{
						throw new RuntimeException();
					}
					// Alexander - First update the buff class on the array
					for (SingleBuff buff : allSingleBuffs)
					{
						if (!buff._buffType.equals(type))
						{
							continue;
						}
						if ((buff._buffId != skillId) || (buff._buffLevel != skillLevel))
						{
							continue;
						}
						buff._forClass = forClass;
					}
					// Alexander - Then update the db
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE npcbuffer_buff_list SET forClass=? WHERE buffId=? AND buffLevel=? AND buffType=?"))
					{
						statement.setInt(1, forClass);
						statement.setInt(2, skillId);
						statement.setInt(3, skillLevel);
						statement.setString(4, type);
						statement.executeUpdate();
					}
					catch (Exception e)
					{
						LOGGER.warning("Error while updating forClass on selected buff" + e);
					}
					msg = viewAllBuffs("set", "Buff_Sets", page);
				}
				else if (eventParam1.equalsIgnoreCase("editSelectedBuff"))
				{
					final int skillId = Integer.parseInt(eventParam2);
					final int skillLevel = Integer.parseInt(eventParam3);
					final int mustEnable = Integer.parseInt(eventParam4);
					final String page = eventSplit[5];
					String typeName = eventSplit[6];
					if (typeName.equalsIgnoreCase("buff"))
					{
						typeName = "Buffs";
					}
					else if (typeName.equalsIgnoreCase("resist"))
					{
						typeName = "Resists";
					}
					else if (typeName.equalsIgnoreCase("song"))
					{
						typeName = "Songs";
					}
					else if (typeName.equalsIgnoreCase("dance"))
					{
						typeName = "Dances";
					}
					else if (typeName.equalsIgnoreCase("chant"))
					{
						typeName = "Chants";
					}
					else if (typeName.equalsIgnoreCase("others"))
					{
						typeName = "Others_Buffs";
					}
					else if (typeName.equalsIgnoreCase("special"))
					{
						typeName = "Special_Buffs";
					}
					else if (typeName.equalsIgnoreCase("cubic"))
					{
						typeName = "Cubics";
					}
					else if (DEBUG)
					{
						throw new RuntimeException();
					}
					// Alexander - First remove or add the buff on the array
					for (SingleBuff buff : allSingleBuffs)
					{
						if (!buff._buffType.equals(eventSplit[6]))
						{
							continue;
						}
						if ((buff._buffId != skillId) || (buff._buffLevel != skillLevel))
						{
							continue;
						}
						buff._canUse = (mustEnable == 1);
					}
					// Alexander - Then update the db
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE npcbuffer_buff_list SET canUse=? WHERE buffId=? AND buffLevel=? AND buffType=?"))
					{
						statement.setInt(1, mustEnable);
						statement.setInt(2, skillId);
						statement.setInt(3, skillLevel);
						statement.setString(4, eventSplit[6]);
						statement.executeUpdate();
					}
					catch (Exception e)
					{
						LOGGER.warning("Error while updating canUse on selected buff" + e);
					}
					msg = viewAllBuffs(eventSplit[6], typeName, page);
				}
				else if (eventParam1.equalsIgnoreCase("giveBuffs"))
				{
					int cost = costDirectBuff(Integer.parseInt(eventParam2));
					if (cost <= 0)
					{
						cost = 100; // Giá mặc định nếu không xác định được chi phí
					}
					// Kiểm tra cấp độ của người chơi, nếu dưới cấp 10, set phí về 0
					if (player.getLevel() < 40)
					{
						cost = 0;
					}
					// Trừ phí nếu người chơi trên hoặc bằng cấp 10
					if (cost > 0 && player.getLevel() > 39)
					{
						if (player.getInventory().getInventoryItemCount(CONSUMABLE_ID, -1) < cost)
						{
							sendErrorMessageToPlayer(player, "You do not have enough necessary items. You need: " + cost + " Adena.");
							return false; // Dừng xử lý nếu không đủ vật phẩm
						}
						// Trừ phí từ người chơi
						player.destroyItemByItemId("Buff Payment", CONSUMABLE_ID, cost, player, true);
					}
					// Kiểm tra các điều kiện khác trước khi áp dụng buff
					if (!isEnabled(Integer.parseInt(eventParam2), Integer.parseInt(eventParam3)) || player.isControlBlocked())
					{
						return false;
					}
					final boolean getpetbuff = isPetBuff(player);
					if (!getpetbuff)
					{
						if (eventParam4.equals("cubic"))
						{
							if (player.getCubics() != null)
							{
								for (Cubic cubic : player.getCubics().values())
								{
									cubic.deactivate();
									player.getCubics().remove(cubic.getTemplate().getId()).deactivate();
								}
							}
						}
						else
						{
							SkillData.getInstance().getSkill(Integer.parseInt(eventParam2), Integer.parseInt(eventParam3)).applyEffects(player, player);
						}
					}
					else
					{
						if (eventParam4.equals("cubic"))
						{
							if (player.getCubics() != null)
							{
								for (Cubic cubic : player.getCubics().values())
								{
									cubic.deactivate();
									player.getCubics().remove(cubic.getTemplate().getId()).deactivate();
								}
							}
						}
						else
						{
							if (player.getPet() != null)
							{
								SkillData.getInstance().getSkill(Integer.parseInt(eventParam2), Integer.parseInt(eventParam3)).applyEffects(player.getPet(), player.getPet());
							}
							else
							{
								sendErrorMessageToPlayer(player, "You do not have a servitor. Summon your pet first!");
								CommunityBoardHandler.separateAndSend(main(player), player);
								return false;
							}
						}
					}
					// Trả về HTML sau khi xử lý
					msg = buildHtml(eventParam4, player);
				}
				else if (eventParam1.equalsIgnoreCase("castBuffSet"))
				{
					if (player.isControlBlocked())
					{
						return false;
					}
					if (player.getLevel() > 39 && !FREE_BUFFS)
					{
						if (player.getInventory().getInventoryItemCount(CONSUMABLE_ID, -1) < BUFF_SET_PRICE)
						{
							sendErrorMessageToPlayer(player, "You do not have enough necessary items. You need: " + BUFF_SET_PRICE + " " + getItemNameHtml(player, CONSUMABLE_ID) + ".");
							return false;
						}
					}
					final List<int[]> buff_sets = new ArrayList<>();
					final int player_class;
					if (player.isMageClass())
					{
						player_class = 1;
					}
					else
					{
						player_class = 0;
					}
					final boolean getpetbuff = isPetBuff(player);
					if (!getpetbuff)
					{
						for (SingleBuff buff : allSingleBuffs)
						{
							if (!buff._canUse)
							{
								continue;
							}
							if ((buff._forClass == player_class) || (buff._forClass == 2))
							{
								buff_sets.add(new int[]
								{
									buff._buffId,
									buff._buffLevel
								});
							}
						}
						ThreadPool.execute(() ->
						{
							for (int[] i : buff_sets)
							{
								SkillData.getInstance().getSkill(i[0], i[1]).applyEffects(player, player);
							}
						});
					}
					else
					{
						if (player.getPet() != null)
						{
							for (SingleBuff buff : allSingleBuffs)
							{
								if (!buff._canUse)
								{
									continue;
								}
								if ((buff._forClass == 0) || (buff._forClass == 2))
								{
									buff_sets.add(new int[]
									{
										buff._buffId,
										buff._buffLevel
									});
								}
							}
							ThreadPool.execute(() ->
							{
								for (int[] i : buff_sets)
								{
									SkillData.getInstance().getSkill(i[0], i[1]).applyEffects(player.getPet(), player.getPet());
								}
							});
						}
						else
						{
							sendErrorMessageToPlayer(player, "You do not have a servitor summoned. Please summon your servitor and try again.");
							CommunityBoardHandler.separateAndSend(main(player), player);
							return false;
						}
					}
					// Chỉ trừ phí nếu nhân vật từ cấp 10 trở lên
					if (player.getLevel() > 39)
					{
						player.destroyItemByItemId("SchemeBuffer", CONSUMABLE_ID, BUFF_SET_PRICE, player, true);
					}
					msg = main(player);
				}
				else if (eventParam1.equalsIgnoreCase("heal"))
				{
					// Kiểm tra cấp độ của người chơi, nếu dưới cấp 40, miễn phí
					int healCost = (player.getLevel() < 40) ? 0 : HEAL_PRICE;

					if (healCost > 0 && player.getInventory().getInventoryItemCount(CONSUMABLE_ID, -1) < healCost)
					{
						sendErrorMessageToPlayer(player, "You don't have enough Adena!");
						CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
						return false;
					}
					if (!canHeal(player))
					{
						sendErrorMessageToPlayer(player, "You cannot heal outside of town!");
						CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
						return false;
					}
					final boolean getpetbuff = isPetBuff(player);
					if (getpetbuff)
					{
						if (player.getPet() != null)
						{
							heal(player, getpetbuff);
						}
						else
						{
							sendErrorMessageToPlayer(player, "You do not have a servitor summoned. Please summon your servitor and try again.");
							CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
							return false;
						}
					}
					else
					{
						heal(player, getpetbuff);
					}
					// Chỉ trừ phí nếu nhân vật từ cấp 40 trở lên
					if (healCost > 0)
					{
						player.destroyItemByItemId("SchemeBuffer", CONSUMABLE_ID, healCost, player, true);
					}
					String costMessage = (player.getLevel() < 40) ? " (Free for players under level 40)" : "";
					sendErrorMessageToPlayer(player, "Heal completed successfully." + costMessage);
					msg = main(player);
				}
				else if (eventParam1.equalsIgnoreCase("removeBuffs"))
				{
					final boolean getpetbuff = isPetBuff(player);
					if (getpetbuff)
					{
						if (player.getPet() != null)
						{
							player.getAnyServitor().getEffectList().stopAllEffects(true);
						}
						else
						{
							sendErrorMessageToPlayer(player, "You do not have a servitor summoned. Please summon your servitor and try again.");
							CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
							return false;
						}
					}
					else
					{
						player.stopAllEffects();
						if (player.getCubics() != null)
						{
							for (Cubic cubic : player.getCubics().values())
							{
								cubic.deactivate();
								player.getCubics().remove(cubic.getTemplate().getId()).deactivate();
							}
						}
					}
					msg = main(player);
				}
				else if (eventParam1.equalsIgnoreCase("cast"))
				{
					int schemeId = Integer.parseInt(eventParam2);
					PlayerScheme currentScheme = player.getBuffSchemeById(schemeId);
					if (currentScheme == null || currentScheme.schemeBuffs.isEmpty())
					{
						sendErrorMessageToPlayer(player, "The selected scheme is empty.");
						return false;
					}
					// Tính tổng chi phí buff (LUÔN kiểm tra level hiện tại để tránh exploit)
					int totalCost = 0;
					if (player.getLevel() < 40)
					{
						// Miễn phí cho người chơi dưới level 40
						totalCost = 0;
					}
					else
					{
						// Tính lại chi phí dựa trên level hiện tại, không dùng giá đã lưu
						for (SchemeBuff buff : currentScheme.schemeBuffs)
						{
							totalCost += costSchemeBuff(buff.skillId); // Tính lại chi phí thực tế
						}
					}
					// Kiểm tra L-Coin
					if (player.getInventory().getInventoryItemCount(CONSUMABLE_ID, -1) < totalCost)
					{
						sendErrorMessageToPlayer(player, "You don't have enough Adena to cast this scheme.");
						return false;
					}
					// Áp dụng tất cả các buff
					for (SchemeBuff buff : currentScheme.schemeBuffs)
					{
						SkillData.getInstance().getSkill(buff.skillId, buff.skillLevel).applyEffects(player, player);
					}
					// Trừ L-Coin tổng
					player.destroyItemByItemId("SchemeBuffer", CONSUMABLE_ID, totalCost, player, true);
					sendErrorMessageToPlayer(player, "Scheme buffed successfully. Total cost: " + totalCost + " Adena.");
					msg = main(player);
				}
				else if (eventParam1.equalsIgnoreCase("manage_scheme_1"))
				{
					msg = viewAllSchemeBuffs(player, eventParam2, eventParam3);
				}
				else if (eventParam1.equalsIgnoreCase("remove_buff"))
				{
					String[] split = eventParam2.split("_");
					String scheme = split[0];
					int skillId = Integer.parseInt(split[1]);
					int skillLevel = Integer.parseInt(split[2]);
					// Lấy scheme hiện tại
					PlayerScheme currentScheme = player.getBuffSchemeById(Integer.parseInt(scheme));
					if (currentScheme == null)
					{
						sendErrorMessageToPlayer(player, "Invalid scheme selected.");
						return false;
					}
					// Tìm buff cần xóa
					SchemeBuff buffToRemove = null;
					for (SchemeBuff buff : currentScheme.schemeBuffs)
					{
						if (buff.skillId == skillId && buff.skillLevel == skillLevel)
						{
							buffToRemove = buff;
							break;
						}
					}
					if (buffToRemove == null)
					{
						sendErrorMessageToPlayer(player, "Buff not found in the scheme.");
						return false;
					}
					// Xóa buff khỏi database
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("DELETE FROM npcbuffer_scheme_contents WHERE scheme_id = ? AND skill_id = ? AND skill_level = ? LIMIT 1"))
					{
						statement.setInt(1, currentScheme.schemeId);
						statement.setInt(2, skillId);
						statement.setInt(3, skillLevel);
						statement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while deleting Scheme Buff: " + e);
						return false;
					}
					// Cập nhật danh sách buff trong memory
					currentScheme.schemeBuffs.remove(buffToRemove);
					currentScheme.totalCostLcoin -= buffToRemove.getPrice();
					// Cập nhật tổng chi phí vào cơ sở dữ liệu
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement updateStatement = con.prepareStatement("UPDATE npcbuffer_scheme_list SET total_lcoin_used = ? WHERE id = ?"))
					{
						updateStatement.setInt(1, currentScheme.totalCostLcoin);
						updateStatement.setInt(2, currentScheme.schemeId);
						updateStatement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while updating total Lcoin cost after removing buff: " + e);
					}
					sendErrorMessageToPlayer(player, "Buff removed from scheme successfully.");
					msg = viewAllSchemeBuffs(player, scheme, eventParam3);
				}
				else if (eventParam1.equalsIgnoreCase("add_buff"))
				{
					String[] split = eventParam2.split("_");
					String scheme = split[0];
					int skillId = Integer.parseInt(split[1]);
					int skillLevel = Integer.parseInt(split[2]);
					// Kiểm tra scheme
					PlayerScheme currentScheme = player.getBuffSchemeById(Integer.parseInt(scheme));
					if (currentScheme == null)
					{
						sendErrorMessageToPlayer(player, "Invalid scheme selected.");
						return false;
					}
					// Kiểm tra xem buff có hợp lệ không
					if (!isEnabled(skillId, skillLevel))
					{
						sendErrorMessageToPlayer(player, "This buff is not enabled.");
						return false;
					}
					int buffCost = costSchemeBuff(skillId); // Giá của buff
					// Kiểm tra cấp độ của người chơi, nếu dưới cấp 40, set phí về 0
					if (player.getLevel() < 40)
					{
						buffCost = 0;
					}
					// Thêm buff vào database
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO npcbuffer_scheme_contents (scheme_id, skill_id, skill_level, buff_class, price) VALUES (?, ?, ?, ?, ?)"))
					{
						statement.setInt(1, currentScheme.schemeId);
						statement.setInt(2, skillId);
						statement.setInt(3, skillLevel);
						statement.setInt(4, getClassBuff(skillId));
						statement.setInt(5, buffCost); // Giá của buff (0 nếu dưới level 40)
						statement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while adding buff to scheme: " + e);
						return false;
					}
					// Cập nhật danh sách buff trong memory
					currentScheme.schemeBuffs.add(new SchemeBuff(skillId, skillLevel, getClassBuff(skillId), buffCost));
					// Cập nhật tổng L-Coins của scheme trong memory
					currentScheme.totalCostLcoin += buffCost;
					// Cập nhật tổng L-Coins vào bảng npcbuffer_scheme_list
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement updateStatement = con.prepareStatement("UPDATE npcbuffer_scheme_list SET total_lcoin_used = ? WHERE id = ?"))
					{
						updateStatement.setInt(1, currentScheme.totalCostLcoin);
						updateStatement.setInt(2, currentScheme.schemeId);
						updateStatement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while updating total Lcoin cost for scheme: " + e);
					}
					String costMessage = (player.getLevel() < 40) ? " (Free for players under level 40)" : "";
					sendErrorMessageToPlayer(player, "Buff added to scheme successfully." + costMessage);
					msg = viewAllSchemeBuffs(player, scheme, eventParam3);
				}
				else if (eventParam1.equalsIgnoreCase("create"))
				{
					String name = getCorrectName(eventParam3 + (eventParam4.equalsIgnoreCase("x") ? "" : " " + eventParam4));
					if (name.isEmpty() || name.equals("no_name"))
					{
						player.sendPacket(new SystemMessage(SystemMessageId.INCORRECT_NAME_PLEASE_TRY_AGAIN));
						sendErrorMessageToPlayer(player, "Please, enter a scheme name.");
						return false;
					}
					int iconId = 0;
					try
					{
						iconId = Integer.parseInt(eventParam2);
						if ((iconId < 0) || (iconId > (SCHEME_ICONS.length - 1)))
						{
							throw new Exception();
						}
					}
					catch (Exception e)
					{
						sendErrorMessageToPlayer(player, "Wrong icon selected!");
						CommunityBoardHandler.separateAndSend(main(player), player);
						return false;
					}
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO npcbuffer_scheme_list (player_id, scheme_name, icon, total_lcoin_used) VALUES (?, ?, ?, ?)", Statement.RETURN_GENERATED_KEYS))
					{
						statement.setInt(1, player.getObjectId());
						statement.setString(2, name);
						statement.setInt(3, iconId);
						statement.setInt(4, 0); // Tổng chi phí L-Coin mặc định là 0
						statement.executeUpdate();
						try (ResultSet rset = statement.getGeneratedKeys())
						{
							if (rset.next())
							{
								int id = rset.getInt(1);
								player.getBuffSchemes().add(new PlayerScheme(id, name, iconId, 0));
								msg = getOptionList(player, id);
								player.getVariables().set("schemeToDel", id);
							}
							else
							{
								LOGGER.warning("Couldn't get Generated Key while creating scheme!");
							}
						}
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while inserting Scheme List" + e);
						msg = main(player);
					}
				}
				else if (eventParam1.equalsIgnoreCase("delete"))
				{
					final int schemeId = Integer.parseInt(eventParam2);
					final PlayerScheme scheme = player.getBuffSchemeById(schemeId);
					if (scheme == null)
					{
						sendErrorMessageToPlayer(player, "Invalid scheme selected.");
						CommunityBoardHandler.separateAndSend(main(player), player);
						return false;
					}
					// askQuestion(player, schemeId, scheme.schemeName);
					deleteScheme(schemeId, player);
					player.getVariables().remove("schemeToDel");
					CommunityBoardHandler.separateAndSend(main(player), player);
					msg = main(player);
				}
				else if (eventParam1.equalsIgnoreCase("delete_c"))
				{
					msg = "<html><head><title>" + TITLE_NAME + "</title></head><body><br><center><img src=\"L2UI_CH3.herotower_deco\" width=256 height=32><br><font name=\"hs12\" color=LEVEL>Do you really want to delete '" + eventParam3 + "' scheme?</font><br><br><button value=\"Yes\" action=\"bypass _bbsbufferbypass delete " + eventParam2 + " x x\" width=50 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">" + "<button value=\"No\" action=\"bypass _bbsbufferbypass delete_1 x x x\" width=50 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></body></html>";
				}
				else if (eventParam1.equalsIgnoreCase("create_1"))
				{
					if (player.getBuffSchemes().size() >= SCHEMES_PER_PLAYER)
					{
						sendErrorMessageToPlayer(player, "You have reached your max scheme count.");
						CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
						return false;
					}
					msg = createScheme(player, Integer.parseInt(eventParam2));
				}
				else if (eventParam1.equalsIgnoreCase("edit_1"))
				{
					msg = getEditSchemePage(player);
				}
				else if (eventParam1.equalsIgnoreCase("delete_1"))
				{
					msg = getDeleteSchemePage(player);
				}
				else if (eventParam1.equalsIgnoreCase("manage_scheme_select"))
				{
					msg = getOptionList(player, Integer.parseInt(eventParam2));
				}
				// Alexander - Function to cast a certain custom buff set
				else if (eventParam1.equalsIgnoreCase("giveBuffSet"))
				{
					if (player.isControlBlocked())
					{
						return false;
					}
					if (player.getLevel() > 39 && !FREE_BUFFS)
					{
						if (player.getInventory().getInventoryItemCount(CONSUMABLE_ID, -1) < 200_000)
						{
							sendErrorMessageToPlayer(player, "You don't have enough Adena! You need" + 200_000 + " Adena!");
							CommunityBoardHandler.separateAndSend(main(player), player); // Resend the main page or the cb will get stucked
							return false;
						}
					}
					final List<int[]> buff_sets;
					switch (eventParam2)
					{
						case "mage":
							buff_sets = Config.NpcBuffer_BuffSetMage;
							break;
						case "dagger":
							buff_sets = Config.NpcBuffer_BuffSetDagger;
							break;
						case "support":
							buff_sets = Config.NpcBuffer_BuffSetSupport;
							break;
						case "tank":
							buff_sets = Config.NpcBuffer_BuffSetTank;
							break;
						case "archer":
							buff_sets = Config.NpcBuffer_BuffSetArcher;
							break;
						default:
						case "fighter":
							buff_sets = Config.NpcBuffer_BuffSetFighter;
							break;
					}
					final boolean getpetbuff = isPetBuff(player);
					if (!getpetbuff)
					{
						ThreadPool.execute(() ->
						{
							for (int[] i : buff_sets)
							{
								SkillData.getInstance().getSkill(i[0], i[1]).applyEffects(player, player);
							}
						});
					}
					else
					{
						if (player.getPet() != null)
						{
							ThreadPool.execute(() ->
							{
								for (int[] i : buff_sets)
								{
									SkillData.getInstance().getSkill(i[0], i[1]).applyEffects(player.getPet(), player.getPet());
								}
							});
						}
						else
						{
							sendErrorMessageToPlayer(player, "You do not have a servitor summoned. Please summon your servitor and try again.");
							CommunityBoardHandler.separateAndSend(main(player), player);
							return false;
						}
					}
					if (player.getLevel() > 39)
					{
						player.destroyItemByItemId("SchemeBuffer", CONSUMABLE_ID, 200_000, player, true);
					}
					String costMessage = (player.getLevel() < 40) ? " (Free for players under level 40)" : "";
					sendErrorMessageToPlayer(player, "Buff set applied successfully." + costMessage);
					msg = main(player);
				}
				// Alexander - Main page for changing scheme name
				else if (eventParam1.equalsIgnoreCase("changeName_1"))
				{
					String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_change_name.htm");
					if (isPetBuff(player))
					{
						dialog = dialog.replace("%topbtn%", (player.getPet() != null ? player.getPet().getName() : "You don't have Pet"));
					}
					else
					{
						dialog = dialog.replace("%topbtn%", player.getName());
					}
					dialog = dialog.replace("%schemeId%", eventParam2);
					msg = dialog;
				}
				// Alexander - Change the scheme's name
				else if (eventParam1.equalsIgnoreCase("changeName"))
				{
					final int schemeId = Integer.parseInt(eventParam2);
					final PlayerScheme scheme = player.getBuffSchemeById(schemeId);
					if (scheme == null)
					{
						sendErrorMessageToPlayer(player, "Invalid scheme selected.");
						CommunityBoardHandler.separateAndSend(main(player), player);
						return false;
					}
					String name = getCorrectName(eventParam3 + (eventParam4.equalsIgnoreCase("x") ? "" : " " + eventParam4));
					if (name.isEmpty() || name.equals("no_name"))
					{
						player.sendPacket(new SystemMessage(SystemMessageId.INCORRECT_NAME_PLEASE_TRY_AGAIN));
						sendErrorMessageToPlayer(player, "Please, enter a scheme name.");
						CommunityBoardHandler.separateAndSend(getOptionList(player, schemeId), player);
						return false;
					}
					scheme.schemeName = name;
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE npcbuffer_scheme_list SET scheme_name=? WHERE id=?"))
					{
						statement.setString(1, name);
						statement.setInt(2, schemeId);
						statement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while updating Scheme List" + e);
					}
					sendErrorMessageToPlayer(player, "Your scheme name changed successfully!");
					msg = getOptionList(player, schemeId);
				}
				// Alexander - Main page for changing scheme icon
				else if (eventParam1.equalsIgnoreCase("changeIcon_1"))
				{
					msg = changeSchemeIcon(player, Integer.parseInt(eventParam2));
				}
				// Alexander - Change the scheme's icon
				else if (eventParam1.equalsIgnoreCase("changeIcon"))
				{
					final int schemeId = Integer.parseInt(eventParam2);
					final PlayerScheme scheme = player.getBuffSchemeById(schemeId);
					if (scheme == null)
					{
						sendErrorMessageToPlayer(player, "Invalid scheme selected!");
						CommunityBoardHandler.separateAndSend(main(player), player);
						return false;
					}
					int iconId = 0;
					try
					{
						iconId = Integer.parseInt(eventParam3);
						if ((iconId < 0) || (iconId > (SCHEME_ICONS.length - 1)))
						{
							throw new Exception();
						}
					}
					catch (Exception e)
					{
						sendErrorMessageToPlayer(player, "Wrong icon selected!");
						CommunityBoardHandler.separateAndSend(getOptionList(player, schemeId), player);
						return false;
					}
					scheme.iconId = iconId;
					try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE npcbuffer_scheme_list SET icon=? WHERE id=?"))
					{
						statement.setInt(1, iconId);
						statement.setInt(2, schemeId);
						statement.executeUpdate();
					}
					catch (SQLException e)
					{
						LOGGER.warning("Error while updating Scheme List" + e);
					}
					sendErrorMessageToPlayer(player, "Scheme Icon changed successfully!");
					msg = getOptionList(player, schemeId);
				}
				CommunityBoardHandler.separateAndSend(msg, player);
			}
		}
		else if (command.startsWith("_bbschangepass"))
		{
			if ((player.getSecretCode() == null) || player.getSecretCode().equalsIgnoreCase("")) // doesn't have a secret code set
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/setsecretcode.html");
				returnHtml = returnHtml.replace("%dtn%", "You don't have an account secret code set, you must set it first before you can change your password.");
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			{
				// has a secret code set
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/passchangemain.html");
				returnHtml = returnHtml.replace("%dtn%", " ");
				returnHtml = replaceVars(player, returnHtml);
			}
		}
		else if (command.startsWith("_bbssetsecret_action "))
		{
			final String errorMsg = setSecretCode(player, command);
			if (errorMsg != null)
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/setsecretcode.html");
				returnHtml = returnHtml.replace("%dtn%", errorMsg);
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/setsecretcode-done.html");
			}
		}
		else if (command.equals("_bbschangesecret"))
		{
			if ((player.getSecretCode() == null) || player.getSecretCode().equalsIgnoreCase("")) // doesn't have a secret code set
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/setsecretcode.html");
				returnHtml = returnHtml.replace("%dtn%", "You don't have a secret code set to begin with, you can set it here.");
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			// has a secret code set
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/changesecretcode.html");
				returnHtml = returnHtml.replace("%dtn%", "");
				returnHtml = replaceVars(player, returnHtml);
			}
		}
		else if (command.equals("_bbssuccess_secret"))
		{
			if ((player.getClan() != null) && player.getClan().isNoticeEnabled())
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/success-secret.html");
				returnHtml = returnHtml.replace("%clan_name%", player.getClan().getName());
				returnHtml = returnHtml.replace("%notice_text%", player.getClan().getNotice());
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/home.html");
				returnHtml = replaceVars(player, returnHtml);
			}
		}
		else if (command.startsWith("_bbschangesecret_action "))
		{
			final String errorMsg = setSecretCode(player, command);
			if (errorMsg != null)
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/changesecretcode.html");
				returnHtml = returnHtml.replace("%dtn%", errorMsg);
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/changesecretcode-done.html");
				returnHtml = replaceVars(player, returnHtml);
			}
		}
		else if (command.startsWith("_bbsconfirmsecret_action "))
		{
			final boolean secretOk = isSecretCodeConfirmed(player, command);
			if (!secretOk)
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/secretcodeconfirmation.html");
				returnHtml = replaceVars(player, returnHtml);
			}
			else
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/secretcodeconfirmation-done.html");
				player.sendMessage("Your character is now fully functional. Thank you!");
			}
		}
		if (command.startsWith("_bbschangepass_action "))
		{
			final String errorMsg = doPasswordChange(player, command);
			if (errorMsg != null)
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/passchangemain.html");
				returnHtml = returnHtml.replace("%dtn%", errorMsg);
			}
			else
			{
				returnHtml = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/password/passchangemain-done.html");
			}
		}
		if (command.startsWith("_bbsofflineFarm") && Config.ENABLE_OFFLINE_PLAY_COMMAND)
		{
			if (Config.OFFLINE_PLAY_PREMIUM && !player.hasPremiumStatus())
			{
				player.sendPacket(new ExShowScreenMessage("This command is only available to premium players.", 5000));
				player.sendMessage("This command is only available to premium players.");
				player.sendPacket(ActionFailed.STATIC_PACKET);
				return false;
			}
			if (!player.isAutoPlaying() || !AutoPlayTaskManager.getInstance().isPlayerAutoHunting(player))
			{
				player.sendPacket(new ExShowScreenMessage("You need to enable auto play before exiting.", 5000));
				player.sendMessage("You need to enable auto play before exiting.");
				player.sendPacket(ActionFailed.STATIC_PACKET);
				return false;
			}
			if (player.isInVehicle() || player.isInsideZone(ZoneId.PEACE))
			{
				player.sendPacket(new ExShowScreenMessage("You may not log out from this location.", 5000));
				player.sendPacket(SystemMessageId.YOU_MAY_NOT_LOG_OUT_FROM_THIS_LOCATION);
				player.sendPacket(ActionFailed.STATIC_PACKET);
				return false;
			}
			if (player.isRegisteredOnEvent())
			{
				player.sendPacket(new ExShowScreenMessage("Cannot use this command while registered on an event.", 5000));
				player.sendMessage("Cannot use this command while registered on an event.");
				player.sendPacket(ActionFailed.STATIC_PACKET);
				return false;
			}
			player.addAction(PlayerAction.OFFLINE_PLAY);
			player.sendPacket(new ConfirmDlg("Do you wish to exit and continue auto play?"));
		}
		else if (command.equals("_bbsreferral"))
		{
			ReferralManager.getInstance().showReferralPageInCommunityBoard(player, 1);
		}
		else if (command.startsWith("_bbsreferral_page"))
		{
			try
			{
				int page = Integer.parseInt(command.split(" ")[1]);
				ReferralManager.getInstance().showReferralPageInCommunityBoard(player, page);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid page number.");
			}
		}
		else if (command.startsWith("_bbsreferral_enter"))
		{
			String referralCode = command.substring(18).trim();
			if (!referralCode.isEmpty())
			{
				if (ReferralManager.getInstance().processReferralCode(player, referralCode))
				{
					player.sendMessage("Referral code successfully entered.");
				}
				else
				{
					player.sendMessage("Failed to enter referral code. Please try again.");
				}
			}
			else
			{
				player.sendMessage("You must enter a referral code.");
			}
			ReferralManager.getInstance().showReferralPageInCommunityBoard(player, 1);
		}
		else if (command.equals("_bbsreferral_referred_rewards"))
		{
			ReferralManager.getInstance().showReferredRewards(player);
		}
		else if (command.equals("_bbsreferral_referrer_rewards"))
		{
			ReferralManager.getInstance().showReferrerRewards(player);
		}
		else if (command.startsWith("_bbsreferral_view_activity "))
		{
			String[] tokens = command.split(" ");
			if (tokens.length > 1)
			{
				try
				{
					int invitedPlayerId = Integer.parseInt(tokens[1]);
					ReferralManager.getInstance().showInvitedPlayerActivity(player, invitedPlayerId);
				}
				catch (NumberFormatException e)
				{
					player.sendMessage("Invalid player ID.");
				}
			}
			else
			{
				player.sendMessage("Invalid referral activity command.");
			}
		}
		else if (command.startsWith("_bbsreferral_claim_reward"))
		{
			try
			{
				String[] tokens = command.split(" ");
				int invitedPlayerId = Integer.parseInt(tokens[1]);
				ReferralManager.getInstance().claimReferrerRewards(player, invitedPlayerId);
			}
			catch (Exception e)
			{
				player.sendMessage("An error occurred while trying to claim rewards.");
				e.printStackTrace();
			}
		}
		else if (command.startsWith("_bbsreferral_claim_rewards "))
		{
			try
			{
				String[] tokens = command.split(" ");
				int invitedPlayerId = Integer.parseInt(tokens[1]);
				ReferralManager.getInstance().claimReferrerRewards(player, invitedPlayerId);
			}
			catch (Exception e)
			{
				player.sendMessage("An error occurred while trying to claim rewards.");
				e.printStackTrace();
			}
		}
		else if (command.startsWith("_bbscustom_referral_home"))
		{
			ReferralManager.getInstance().showReferralPageInCommunityBoard(player, 1);
		}
		else if (command.equals("_bbsreferral_cancel_referral"))
		{
			ReferralManager.getInstance().cancelReferral(player);
			ReferralManager.getInstance().showReferralPageInCommunityBoard(player, 1);
		}
		if (command.startsWith("_bbshowCollection"))
		{
			CollectionManager.getInstance().showCollectionHome(player);
		}
		if (command.startsWith("_bbshowCategory"))
		{
			String[] args = command.split(" ");
			if (args.length < 2)
			{
				player.sendMessage("Invalid category command.");
				return false;
			}
			try
			{
				int category = Integer.parseInt(args[1]);
				int page = args.length > 2 ? Integer.parseInt(args[2]) : 1;
				CollectionManager.getInstance().showCategory(player, category, page);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid category or page number.");
			}
		}
		if (command.startsWith("_bbshowCollectionDetails"))
		{
			String[] params = command.split(" ");
			if (params.length < 4)
			{
				player.sendMessage("Invalid collection details command.");
				return false;
			}
			try
			{
				int collectionId = Integer.parseInt(params[1]);
				int currentCategory = Integer.parseInt(params[2]);
				int currentPage = Integer.parseInt(params[3]);
				CollectionManager.getInstance().showCollectionDetails(player, collectionId, currentCategory, currentPage);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid category, page, or collection ID.");
			}
		}
		if (command.startsWith("_bbcollectItem"))
		{
			String[] params = command.split(" ");
			if (params.length == 6)
			{
				try
				{
					int collectionId = Integer.parseInt(params[1]);
					int itemId = Integer.parseInt(params[2]);
					int slot = Integer.parseInt(params[3]);
					int currentCategory = Integer.parseInt(params[4]);
					int currentPage = 1;
					CollectionManager.getInstance().collectItem(player, collectionId, itemId, slot, currentCategory, currentPage);
				}
				catch (NumberFormatException e)
				{
					player.sendMessage("Invalid collection, item, slot, or category number.");
				}
			}
			else
			{
				player.sendMessage("Invalid collect item command.");
			}
		}
		if (command.startsWith("_bbsbuyCollection"))
		{
			String[] params = command.split(" ");
			if (params.length < 4)
			{
				player.sendMessage("Invalid buy collection command.");
				return false;
			}
			try
			{
				int collectionId = Integer.parseInt(params[1]);
				int currentCategory = Integer.parseInt(params[2]);
				int currentPage = Integer.parseInt(params[3]);
				CollectionManager.getInstance().buyCollection(player, collectionId, currentCategory, currentPage);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid collection, category, or page number.");
			}
		}
		if (command.startsWith("_bbadminPlayerManagement"))
		{
			CollectionManager.getInstance().showPlayerManagement(player);
		}
		if (command.startsWith("_bbadminViewPlayerCollections"))
		{
			String[] args = command.split(" ");
			if (args.length < 2)
			{
				player.sendMessage("Invalid player name command.");
				return false;
			}
			String playerName = command.substring(command.indexOf(" ") + 1).trim();
			if (playerName.isEmpty())
			{
				player.sendMessage("Player name is empty.");
				return false;
			}
			CollectionManager.getInstance().viewPlayerCollections(player, playerName);
		}
		if (command.startsWith("_bbadminAddCollection"))
		{
			String[] args = command.split(" ");
			if (args.length < 3)
			{
				player.sendMessage("Invalid add collection command.");
				return false;
			}
			try
			{
				String targetPlayerName = args[1];
				int collectionId = Integer.parseInt(args[2]);
				Player targetPlayer = World.getInstance().getPlayer(targetPlayerName);
				if (targetPlayer == null)
				{
					player.sendMessage("Player " + targetPlayerName + " not found.");
					return false;
				}
				CollectionManager.getInstance().addCollection(targetPlayer, collectionId);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid collection ID.");
			}
			return true;
		}
		if (command.startsWith("_bbadminDeleteCollection"))
		{
			String[] args = command.split(" ");
			if (args.length < 3)
			{
				player.sendMessage("Invalid delete collection command.");
				return false;
			}
			try
			{
				int playerId = Integer.parseInt(args[1]);
				int collectionId = Integer.parseInt(args[2]);
				CollectionManager.getInstance().deleteCollection(player, playerId, collectionId);
			}
			catch (NumberFormatException e)
			{
				player.sendMessage("Invalid player ID or collection ID.");
			}
		}
		if (Config.ENABLE_DROP_CALCULATOR)
		{
			StringTokenizer st = new StringTokenizer(command, "_");
			st.nextToken();
			if (command.startsWith("_bbssearchdropCalc"))
			{
				showMainPage(player);
			}
			else if (command.startsWith("_bbssearchdropItemsByName_"))
			{
				if (!st.hasMoreTokens())
				{
					showMainPage(player);
					return false;
				}
				String itemName = st.nextToken().trim();
				int itemsPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
				int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
				returnHtml = showDropItemsByNamePage(player, itemName, itemsPage, sortMethod);
			}
			else if (command.startsWith("_bbssearchdropItemsByName_"))
			{
				if (!st.hasMoreTokens())
				{
					showMainPage(player);
					return false;
				}
				String itemName = st.nextToken().trim();
				int itemsPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
				int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
				returnHtml = showDropItemsByNamePage(player, itemName, itemsPage, sortMethod);
			}
			else if (command.startsWith("_bbssearchdropMonstersByItem_"))
			{
				int itemId = Integer.parseInt(st.nextToken());
				int monstersPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
				int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
				returnHtml = showDropMonstersByItem(player, itemId, monstersPage, sortMethod);
			}
			else if (command.startsWith("_bbssearchdropMonsterDetailsByItem_"))
			{
				int monsterId = Integer.parseInt(st.nextToken());
				returnHtml = showdropMonsterDetailsByItem(player, monsterId);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				if (st.hasMoreTokens())
				{
					manageButton(player, Integer.parseInt(st.nextToken()), monsterId);
				}
				return false;
			}
			else if (command.startsWith("_bbssearchdropMonstersByName_"))
			{
				try
				{
					if (!st.hasMoreTokens())
					{
						player.sendMessage("Monster name is missing.");
						return false;
					}
					String monsterName = st.nextToken().trim();
					int page = 1; // Mặc định là trang 1
					if (st.hasMoreTokens())
					{
						try
						{
							page = Integer.parseInt(st.nextToken());
						}
						catch (NumberFormatException e)
						{
							player.sendMessage("Invalid page number. Defaulting to page 1.");
						}
					}
					int sort = 0;
					if (st.hasMoreTokens())
					{
						try
						{
							sort = Integer.parseInt(st.nextToken());
						}
						catch (NumberFormatException e)
						{
							player.sendMessage("Invalid sorting option. Defaulting to sort 0.");
						}
					}
					String html = showDropMonstersByName(player, monsterName, page, sort);
					CommunityBoardHandler.separateAndSend(html, player);
				}
				catch (NoSuchElementException e)
				{
					player.sendMessage("There was an error processing your request.");
					e.printStackTrace();
				}
				// if (!st.hasMoreTokens())
				// {
				// showMainPage(player);
				// return false;
				// }
				// String monsterName = st.nextToken().trim();
				// int page = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
				// int sort = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
				// String html = showDropMonstersByName(player, monsterName, page, sort);
				// CommunityBoardHandler.separateAndSend(html, player);
			}
			else if (command.startsWith("_bbssearchdropMonsterDetailsByName_"))
			{
				int chosenMobId = Integer.parseInt(st.nextToken());
				returnHtml = showDropMonsterDetailsByName(player, chosenMobId);
				CommunityBoardHandler.separateAndSend(returnHtml, player);
				if (st.hasMoreTokens())
				{
					manageButton(player, Integer.parseInt(st.nextToken()), chosenMobId);
				}
			}
			else if (command.startsWith("_bbssearchNpcDropList"))
			{
				player.getVariables().set("DCDropType", command.split("_")[2]);
				DropInfoFunctions.showNpcDropList(player, command.split("_")[2], Integer.parseInt(command.split("_")[3]), Integer.parseInt(command.split("_")[4]));
			}
			else if (command.startsWith("_bbssearchShowSkills"))
			{
				DropInfoFunctions.showNpcSkillList(player, Integer.parseInt(command.split("_")[2]), Integer.parseInt(command.split("_")[3]));
			}
			if (command.startsWith("_bbspartymatching"))
			{
				PartyMatchingBBSManager.getInstance().parsecmd(command, player);
			}
			else if (command.startsWith("_bbspartymatchinginvite"))
			{
				String targetName = command.substring(24);
				Player receiver = World.getInstance().getPlayer(targetName);
				SystemMessage sm;
				if (receiver == null)
				{
					player.sendPacket(SystemMessageId.YOU_MUST_FIRST_SELECT_A_USER_TO_INVITE_TO_YOUR_PARTY);
				}
				else if ((receiver.getClient() == null) || receiver.getClient().isDetached())
				{
					player.sendMessage("Player is in offline mode.");
				}
				else if (!player.canOverrideCond(PlayerCondOverride.SEE_ALL_PLAYERS) && receiver.isInvisible())
				{
					player.sendPacket(SystemMessageId.THAT_IS_AN_INCORRECT_TARGET);
				}
				else if (receiver.isInParty())
				{
					sm = new SystemMessage(SystemMessageId.C1_IS_A_MEMBER_OF_ANOTHER_PARTY_AND_CANNOT_BE_INVITED);
					sm.addString(receiver.getName());
					player.sendPacket(sm);
				}
				else if (BlockList.isBlocked(receiver, player))
				{
					sm = new SystemMessage(SystemMessageId.C1_HAS_PLACED_YOU_ON_HIS_HER_IGNORE_LIST);
					sm.addPcName(receiver);
					player.sendPacket(sm);
				}
				else if (receiver == player)
				{
					player.sendPacket(SystemMessageId.YOU_HAVE_INVITED_THE_WRONG_TARGET);
				}
				else if (receiver.isCursedWeaponEquipped() || player.isCursedWeaponEquipped())
				{
					receiver.sendPacket(SystemMessageId.INVALID_TARGET);
				}
				// else if (receiver.isInJail() || player.isInJail())
				// {
				// player.sendMessage("You cannot invite a player while is in Jail.");
				// }
				else if (receiver.isInOlympiadMode() || player.isInOlympiadMode())
				{
					if ((receiver.isInOlympiadMode() != player.isInOlympiadMode()) || (receiver.getOlympiadGameId() != player.getOlympiadGameId()) || (receiver.getOlympiadSide() != player.getOlympiadSide()))
					{
						player.sendPacket(SystemMessageId.A_USER_CURRENTLY_PARTICIPATING_IN_THE_OLYMPIAD_CANNOT_SEND_PARTY_AND_FRIEND_INVITATIONS);
						return false;
					}
				}
				else
				{
					sm = new SystemMessage(SystemMessageId.C1_HAS_BEEN_INVITED_TO_THE_PARTY);
					sm.addPcName(receiver);
					player.sendPacket(sm);
					if (!player.isInParty())
					{
						createNewParty(receiver, player);
					}
					else
					{
						addTargetToParty(receiver, player);
					}
				}
				returnHtml = sendHtm(player, "main", command);
			}
			else if (command.startsWith("_bbspartymatchinglist"))
			{
				String[] value = command.split(" ");
				String type = value[1];
				if (type.equals("on"))
				{
					if (player.isInParty())
					{
						player.sendMessage("You can't use this while you're in party!");
					}
					player.getVariables().set("partyMatch", true);
					player.sendMessage("You've entered the party matching list.");
				}
				else if (type.equals("off"))
				{
					if (player.isInParty())
					{
						player.sendMessage("You can't use this while you're in party!");
						return false;
					}
					player.getVariables().remove("partyMatch");
					player.sendMessage("You've left the party matching list.");
				}
				returnHtml = sendHtm(player, "main", command);
			}
			else if (command.startsWith("_bbspartymatchingrefresh"))
			{
				returnHtml = sendHtm(player, "main", command);
			}
		}
		if (command.startsWith("_bbsstats"))
		{
			NpcHtmlMessage html = new NpcHtmlMessage(1);
			StringBuilder html1 = new StringBuilder("<html><title>Player Stats</title><body>");
			html1.append("<br><center><font color=\"LEVEL\">[Additional Player Stats]</font></center>");
			html1.append("<table border=0 width=\"100%\">");
			html1.append("<tr><td>Critical Damage Multi</td><td>" + new DecimalFormat("0.##").format(player.getCriticalDmg((int) 1.66)) + "x +" + player.getStat().getValue(Stat.CRITICAL_DAMAGE_ADD, 0) + "</td></tr>");
			html1.append("<tr><td>Magic Critical Rate</td><td>" + Math.round(player.getMCriticalHit() / 10) + "%" + "</td></tr>");
			html1.append("<tr><td>Skill Reuse Delay</td><td>" + (int) (player.getStat().getReuseTypeValue(0) * 100) + "%" + "</td></tr>");
			html1.append("<tr><td>Magic Reuse Delay</td><td>" + (int) (player.getStat().getReuseTypeValue(1) * 100) + "%" + "</td></tr>");
			double shldRate = player.getStat().getValue(Stat.SHIELD_DEFENCE_RATE) * BaseStat.CON.calcBonus(player);
			html1.append("<tr><td>Shield Defense Rate</td><td>" + shldRate + "%" + "</td></tr>");
			html1.append("<tr><td>Shield Defense</td><td>" + player.getShldDef() + "</td></tr>");
			html1.append("<tr><td>Healed Boost (received)</td><td>" + (int) (player.getStat().getValue(Stat.HEAL_EFFECT_ADD, 100)) + "%" + "</td></tr>");
			html1.append("<tr><td>Healing Power (given)</td><td>" + (int) (player.getStat().getValue(Stat.HEAL_EFFECT, 100)) + "%" + "</td></tr>");
			html1.append("<tr><td>PVP Attack Hits Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVP Physical Skill Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVP Magical Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVP Attack Hits Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVP Physical Skill Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVP Magical Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Attack Hits Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_PHYSICAL_ATTACK_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Physical Skill Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_PHYSICAL_SKILL_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Magical Skill Damage</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_MAGICAL_SKILL_DAMAGE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Attack Hits Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_PHYSICAL_ATTACK_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Physical Skill Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_PHYSICAL_SKILL_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>PVE Magical Def</td><td>" + new DecimalFormat("0.##").format(player.getStat().getValue(Stat.PVE_MAGICAL_SKILL_DEFENCE, 1)) + "x" + "</td></tr>");
			html1.append("<tr><td>Physical Skill Dodge</td><td>" + (int) (player.getStat().getSkillEvasionTypeValue(0)) + "%" + "</td></tr>");
			html1.append("<tr><td>Magic Skill Dodge</td><td>" + (int) (player.getStat().getSkillEvasionTypeValue(1)) + "%" + "</td></tr>");
			html1.append("<tr><td>Attack Range</td><td>" + player.getPhysicalAttackRange() + "</td></tr>");
			html1.append("<tr><td>Cast Range</td><td>" + "skill default +" + player.getStat().getMagicalAttackRange(null) + "</td></tr>");
			html1.append("<tr><td>Damage Reflect</td><td>" + (int) (player.getStat().getValue(Stat.REFLECT_DAMAGE_PERCENT, 0)) + "%" + "</td></tr>");
			html1.append("<tr><td>Skill Reflect</td><td>" + (int) (player.getStat().getValue(Stat.REFLECT_SKILL_PHYSIC, 0)) + "%" + "</td></tr>");
			html1.append("<tr><td>Magic Reflect</td><td>" + (int) (player.getStat().getValue(Stat.REFLECT_SKILL_MAGIC, 0)) + "%" + "</td></tr>");
			html1.append("<tr><td>HP Regen</td><td>" + (int) (player.getStat().getValue(Stat.REGENERATE_HP_RATE)) + " per tick" + "</td></tr>");
			html1.append("<tr><td>Vamp. HP Absorb %</td><td>" + (int) (player.getStat().getValue(Stat.ABSORB_DAMAGE_PERCENT, 0)) + "%" + "</td></tr><br><br>");
			html1.append("<tr><td>Critical Dmg Def</td><td>" + player.getStat().getValue(Stat.DEFENCE_CRITICAL_DAMAGE, 1) + "</td></tr><br><br>");
			html1.append("<tr><td>Magic Crit Dmg Skill</td><td>" + player.getStat().getValue(Stat.MAGIC_CRITICAL_DAMAGE, 1) + "</td></tr><br><br>");
			html1.append("<tr><td>Magical Damage Resist</td><td>" + player.getStat().getValue(Stat.DEFENCE_MAGIC_CRITICAL_DAMAGE, 1) + "</td></tr><br><br>");
			final int atkCount = (int) (player.getStat().getValue(Stat.ATTACK_COUNT_MAX, 1));
			html1.append("<tr><td>Attack Count</td><td>" + atkCount + "</td></tr><br><br>");
			// html1.append("<tr><td>Attack AOE Angle</td><td>" + (atkCount > 1 ? (int) (player.getStat().calcStat(Stats.POWER_ATTACK_ANGLE, 120, null, null)) : "N/A") + "</td></tr><br><br>");
			html1.append("</table>");
			html1.append("<center><button value=\"Refesh\" action=\"bypass -h _bbsstats\" width=90 height=23 back=\"L2UI_ct1.button_df_down\" fore=\"L2UI_ct1.button_df\"></td></center>");
			html1.append("</body></html>");
			html.setHtml(html1.toString());
			player.sendPacket(html);
		}
		if (command.equalsIgnoreCase("_bbsstats_attack"))
		{
			NumberFormat df = NumberFormat.getInstance(Locale.ENGLISH);
			df.setMaximumFractionDigits(1);
			df.setMinimumFractionDigits(1);
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/stats/attack.htm");
			// SS
			double ss = player.getActiveRubyJewel() != null ? player.getActiveRubyJewel().getBonus() * 100 : 0;
			double bss = player.getActiveShappireJewel() != null ? player.getActiveShappireJewel().getBonus() * 100 : 0;
			// int ruby = player.getActiveRubyJewel() != null ? (int) player.getActiveRubyJewel().getBonus() : 0;
			// int sapphire = player.getActiveRubyJewel() != null ? (int) player.getActiveShappireJewel().getBonus() : 0;
			dialog = dialog.replace("%ss_damage_active%", String.valueOf(ss) + "%");
			dialog = dialog.replace("%bss_damage_active%", String.valueOf(bss) + "%");
			// PVP
			dialog = dialog.replace("%basic_pvp_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DAMAGE) + "% - " + (int) player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%p_skill_pvp_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DAMAGE, 0) + "% - " + (int) player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%m_skill_pvp_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DAMAGE, 0) + "% - " + (int) player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%pvp_damage_taken%", String.valueOf((int) player.getStat().getValue(Stat.PVP_DAMAGE_TAKEN, 0) + "%"));
			// PVE
			dialog = dialog.replace("%basic_pve_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVE_PHYSICAL_ATTACK_DAMAGE, 0) + "% - " + (int) player.getStat().getValue(Stat.PVE_PHYSICAL_ATTACK_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%p_skill_pve_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVE_PHYSICAL_SKILL_DAMAGE, 0) + "% - " + (int) player.getStat().getValue(Stat.PVE_PHYSICAL_SKILL_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%m_skill_pve_damage_def%", String.valueOf((int) player.getStat().getValue(Stat.PVE_MAGICAL_SKILL_DAMAGE, 0) + "% - " + (int) player.getStat().getValue(Stat.PVE_MAGICAL_SKILL_DEFENCE, 0) + "%"));
			dialog = dialog.replace("%pve_damage_taken%", String.valueOf((int) player.getStat().getValue(Stat.PVE_DAMAGE_TAKEN, 0) + "%"));
			// Power
			dialog = dialog.replace("%p_skill_power%", String.valueOf((int) player.getStat().getValue(Stat.PHYSICAL_SKILL_POWER, 0)) + "%");
			dialog = dialog.replace("%m_skill_power%", String.valueOf((int) player.getStat().getValue(Stat.MAGICAL_SKILL_POWER, 0)) + "%");
			// Critical Rate Num/Per
			dialog = dialog.replace("%critical_damage%", df.format(player.getCriticalDmg((int) 1.66)) + "x +" + player.getStat().getValue(Stat.CRITICAL_DAMAGE_ADD, 0));
			dialog = dialog.replace("%p_critical_dmg%", String.valueOf((int) player.getStat().getValue(Stat.PHYSICAL_SKILL_CRITICAL_DAMAGE, 1)) + "x +" + player.getStat().getValue(Stat.PHYSICAL_SKILL_CRITICAL_DAMAGE_ADD, 0));
			dialog = dialog.replace("%Mcritical_damage%", String.valueOf(player.getStat().getValue(Stat.MAGIC_CRITICAL_DAMAGE, 1)) + "x +" + player.getStat().getValue(Stat.MAGIC_CRITICAL_DAMAGE_ADD, 0));
			dialog = dialog.replace("%fatal_blow%", df.format(player.getStat().getMul(Stat.BLOW_RATE, 0)) + "%");
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.equalsIgnoreCase("_bbsstats_defence"))
		{
			NumberFormat df = NumberFormat.getInstance(Locale.ENGLISH);
			df.setMaximumFractionDigits(1);
			df.setMinimumFractionDigits(1);
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/stats/defence.htm");
			// Shield DEF
			Item shld = player.getSecondaryWeaponInstance();
			boolean shield = (shld != null) && (shld.getItemType() == WeaponType.NONE);
			dialog = dialog.replace("%shield_def%", shield ? String.valueOf(player.getStat().getValue(Stat.SHIELD_DEFENCE, player.getTemplate().getBaseShldDef())) : "0");
			int shldRate = (int) (player.getStat().getValue(Stat.SHIELD_DEFENCE_RATE) * BaseStat.CON.calcBonus(player));
			dialog = dialog.replace("%shield_rate%", shldRate + "%");
			// PVP Defence
			dialog = dialog.replace("%received_basic_pvp_dmg%", String.valueOf(player.getStat().getValue(Stat.PVP_PHYSICAL_ATTACK_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_p_skill_pvp_dmg%", String.valueOf(player.getStat().getValue(Stat.PVP_PHYSICAL_SKILL_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_m_skill_pvp_dmg%", String.valueOf(player.getStat().getValue(Stat.PVP_MAGICAL_SKILL_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_pvp_dmg_taken%", String.valueOf(player.getStat().getValue(Stat.PVP_DAMAGE_TAKEN, 0)) + "%");
			// PVE
			dialog = dialog.replace("%received_basic_pve_dmg%", String.valueOf(player.getStat().getValue(Stat.PVE_PHYSICAL_ATTACK_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_p_skill_pve_dmg%", String.valueOf(player.getStat().getValue(Stat.PVE_PHYSICAL_SKILL_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_m_skill_pve_dmg%", String.valueOf(player.getStat().getValue(Stat.PVE_MAGICAL_SKILL_DEFENCE, 0)) + "%");
			dialog = dialog.replace("%received_pve_dmg_taken%", String.valueOf(player.getStat().getValue(Stat.PVE_DAMAGE_TAKEN, 0)) + "%");
			dialog = dialog.replace("%m_dmg_reflection%", String.valueOf(player.getStat().getValue(Stat.REFLECT_DAMAGE_PERCENT, 0)) + "%");
			dialog = dialog.replace("%m_dmg_reflection_resist%", String.valueOf(player.getStat().getValue(Stat.REFLECT_DAMAGE_PERCENT_DEFENSE, 0)) + "%");
			dialog = dialog.replace("%received_fixed_dmg%", String.valueOf(player.getStat().getValue(Stat.REAL_DAMAGE_RESIST, 0)) + "%");
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.equalsIgnoreCase("_bbsstats_utility"))
		{
			NumberFormat df = NumberFormat.getInstance(Locale.ENGLISH);
			df.setMaximumFractionDigits(1);
			df.setMinimumFractionDigits(1);
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/stats/utility.htm");
			dialog = dialog.replace("%casting_interruption%", String.valueOf(player.getStat().getValue(Stat.ATTACK_CANCEL, 0)) + "%");
			dialog = dialog.replace("%skill_evasion%", String.valueOf(Math.round((player.getStat().getValue(Stat.EVASION_RATE, 0)))) + "%");
			dialog = dialog.replace("%m_skill_evasion%", String.valueOf(Math.round((player.getStat().getValue(Stat.MAGIC_EVASION_RATE, 0)))) + "%");
			dialog = dialog.replace("%hp_vamp%", String.valueOf(player.getStat().getValue(Stat.ABSORB_DAMAGE_PERCENT, 0) * 100) + "%");
			dialog = dialog.replace("%mana_vamp%", String.valueOf(player.getStat().getValue(Stat.ABSORB_MANA_DAMAGE_PERCENT, 0) * 100) + "%");
			// HP Recovery
			dialog = dialog.replace("%hp_recovery_add%", String.valueOf((int) player.getStat().getValue(Stat.ADDITIONAL_POTION_HP)));
			dialog = dialog.replace("%mp_recovery_add%", String.valueOf((int) player.getStat().getValue(Stat.ADDITIONAL_POTION_MP)));

			// Premium HP/MP/CP Regen bonuses - Display actual values
			boolean hasPremium = player.hasPremiumStatus();

			// Get base regen values (without PA bonus)
			int baseHpRegen = (int) player.getStat().getValue(Stat.REGENERATE_HP_RATE);
			int baseMpRegen = (int) player.getStat().getValue(Stat.REGENERATE_MP_RATE);
			int baseCpRegen = (int) player.getStat().getValue(Stat.REGENERATE_CP_RATE);

			// Display logic: Show base for non-premium, base + PA bonus for premium
			dialog = dialog.replace("%hp_recovery_rate%", String.valueOf(baseHpRegen) + " per tick");
			dialog = dialog.replace("%mp_recovery_rate%", String.valueOf(baseMpRegen) + " per tick");
			dialog = dialog.replace("%cp_recovery_rate%", String.valueOf(baseCpRegen) + " per tick");
			// Cooldown
			dialog = dialog.replace("%physical_reuse%", df.format(player.getStat().getReuseTypeValue(0) * 100) + "%");
			dialog = dialog.replace("%magical_reuse%", df.format(player.getStat().getReuseTypeValue(1) * 100) + "%");
			dialog = dialog.replace("%song_dance_reuse%", df.format(player.getStat().getReuseTypeValue(3) * 100) + "%");

			// MP Skill Cost - Show base for non-premium, base + PA reduction for premium
			double basePhysicalSkillCost = player.getStat().getMpConsumeTypeValue(0) * 100;
			double baseMagicalSkillCost = player.getStat().getMpConsumeTypeValue(1) * 100;
			double baseSongDanceSkillCost = player.getStat().getMpConsumeTypeValue(3) * 100;

			// Apply PA 15% reduction if premium
			double physicalSkillCost = hasPremium ? basePhysicalSkillCost * 0.8 : basePhysicalSkillCost;
			double magicalSkillCost = hasPremium ? baseMagicalSkillCost * 0.8 : baseMagicalSkillCost;
			double songDanceSkillCost = hasPremium ? baseSongDanceSkillCost * 0.8 : baseSongDanceSkillCost;

			dialog = dialog.replace("%physical_skill_cost%", df.format(physicalSkillCost) + "%");
			dialog = dialog.replace("%magical_skill_cost%", df.format(magicalSkillCost) + "%");
			dialog = dialog.replace("%song_dance_skill_cost%", df.format(songDanceSkillCost) + "%");
			dialog = dialog.replace("%healing_power%", String.valueOf((int) player.getStat().getValue(Stat.HEAL_EFFECT, 100)) + "%");
			dialog = dialog.replace("%healing_received%", String.valueOf((int) player.getStat().getValue(Stat.HEAL_EFFECT_ADD, 100)) + "%");
			dialog = dialog.replace("%mana_received%", String.valueOf((int) player.getStat().getValue(Stat.MANA_CHARGE, 100)) + "%");
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.equalsIgnoreCase("_bbsstats_debuff"))
		{
			NumberFormat df = NumberFormat.getInstance(Locale.ENGLISH);
			df.setMaximumFractionDigits(1);
			df.setMinimumFractionDigits(1);
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/stats/debuff.htm");
			dialog = dialog.replace("%poison_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.POISON) * 100)) + "%");
			dialog = dialog.replace("%hold_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.HOLD) * 100)) + "%");
			dialog = dialog.replace("%bleed_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.BLEED) * 100)) + "%");
			dialog = dialog.replace("%sleep_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.SLEEP) * 100)) + "%");
			dialog = dialog.replace("%stun_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.SHOCK) * 100)) + "%");
			dialog = dialog.replace("%fear_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100)) + "%");
			dialog = dialog.replace("%silence_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100)) + "%");
			dialog = dialog.replace("%paralyze_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.PARALYZE) * 100)) + "%");
			dialog = dialog.replace("%knock_back_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.KNOCKBACK) * 100)) + "%");
			dialog = dialog.replace("%knock_down_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.KNOCKDOWN) * 100)) + "%");
			dialog = dialog.replace("%pull_vul%", String.valueOf(Math.round(player.getStat().getDefenceTrait(TraitType.PULL) * 100)) + "%");
			dialog = dialog.replace("%debuff_vul%", String.valueOf(Math.abs(Math.round((player.getStat().getMul(Stat.RESIST_ABNORMAL_DEBUFF) - 1) * 100))) + "%");
			dialog = dialog.replace("%cancel_buff_vul%", String.valueOf(Math.abs(Math.round((player.getStat().getMul(Stat.RESIST_DISPEL_BUFF) - 1) * 100))) + "%");
			// Attack Trait
			dialog = dialog.replace("%poison_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.POISON) - 1) * 100)) + "%");
			dialog = dialog.replace("%hold_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.HOLD) - 1) * 100)) + "%");
			dialog = dialog.replace("%bleed_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.BLEED) - 1) * 100)) + "%");
			dialog = dialog.replace("%sleep_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.SLEEP) - 1) * 100)) + "%");
			dialog = dialog.replace("%stun_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.SHOCK) - 1) * 100)) + "%");
			dialog = dialog.replace("%fear_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100)) + "%");
			dialog = dialog.replace("%silence_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100)) + "%");
			dialog = dialog.replace("%paralyze_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.PARALYZE) - 1) * 100)) + "%");
			dialog = dialog.replace("%knock_back_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.KNOCKBACK) - 1) * 100)) + "%");
			dialog = dialog.replace("%knock_down_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.KNOCKDOWN) - 1) * 100)) + "%");
			dialog = dialog.replace("%pull_power%", String.valueOf(Math.round((player.getStat().getAttackTrait(TraitType.PULL) - 1) * 100)) + "%");
			// dialog = dialog.replace("%debuff_power%", String.valueOf(Math.abs(Math.round((player.getStat().getMul(Stat.RESIST_ABNORMAL_DEBUFF) - 1) * 100))) + "%");
			// dialog = dialog.replace("%cancel_buff_power%", String.valueOf(Math.abs(Math.round((player.getStat().getMul(Stat.RESIST_DISPEL_BUFF) - 1) * 100))) + "%");
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbsgrandbossstatus"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/grandboss/home.html");
			StringBuilder grandbossInfo = new StringBuilder();
			for (Entry<Integer, Integer> entry : GrandBossManager.getInstance()._bossStatus.entrySet())
			{
				int npcId = entry.getKey();
				int status = GrandBossManager.getInstance().getStatus(npcId);
				StatSet info = GrandBossManager.getInstance().getStatSet(npcId);
				String grandbossName = NpcData.getInstance().getTemplate(npcId).getName();
				String liveOrAlive = (status == 1) ? "Dead" : "Alive";
				String respawnTime = (status == 1) ? (info.getLong("respawn_time") > 0 ? TimeUtil.getDateTimeString(info.getLong("respawn_time")) : "Unknown") : "N/A";
				String liveOrAliveColor = liveOrAlive.equals("Alive") ? "00FF00" : "FF0000";
				String respawnTimeColor = respawnTime.equals("Unknown") ? "FFFF00" : "FF0000";
				grandbossInfo.append("<table width=\"240\" border=\"0\" cellspacing=\"0\" cellpadding=\"2\">").append("<tr><td><font color=\"FFFF00\" name=\"hs12\">" + grandbossName + "</font></td></tr>").append("<tr><td><font color=\"" + respawnTimeColor + "\">Respawn Time: " + respawnTime + "</font></td></tr>").append("<tr><td><font color=\"" + liveOrAliveColor + "\">Status: " + liveOrAlive + "</font></td></tr>").append("</table>").append("<img src=\"L2UI.SquareWhite\" width=270 height=1>").append("<br>");
			}
			dialog = dialog.replace("%grandbossList%", grandbossInfo.toString());
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_townmode"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			if (player.isTownMode())
			{
				player.getVariables().set(PlayerVariables.TOWN_MODE, false);
				player.setTownMode(false);
				player.sendMessage("Town mode disabled.");
			}
			else
			{
				player.getVariables().set(PlayerVariables.TOWN_MODE, true);
				player.setTownMode(true);
				player.sendMessage("Town mode enabled.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_2"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			if (player.isTargetRaidMode())
			{
				player.getVariables().set(PlayerVariables.TARGET_RAID, false);
				player.setTargetRaidMode(false);
				player.sendMessage("Target Raid mode disabled.");
			}
			else
			{
				player.getVariables().set(PlayerVariables.TARGET_RAID, true);
				player.setTargetRaidMode(true);
				player.sendMessage("Target Raid mode enabled.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_3"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			if (player.isShowRange())
			{
				player.getVariables().set(PlayerVariables.SHOW_RANGE, false);
				player.setShowRange(false);
				player.sendMessage("Show Range mode disabled.");
			}
			else
			{
				player.getVariables().set(PlayerVariables.SHOW_RANGE, true);
				player.setShowRange(true);
				player.sendMessage("Show Range mode enabled.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_4"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			// Kiểm tra xem người chơi có party hay không
			if (player.getParty() == null)
			{
				player.sendMessage("You must be in a party to enable Heal mode.");
			}
			else
			{
				if (player.isEnableHeal())
				{
					player.getVariables().set(PlayerVariables.ENABLE_HEAL, false);
					player.setEnableHeal(false);
					player.sendMessage("Heal mode disabled.");
				}
				else
				{
					player.getVariables().set(PlayerVariables.ENABLE_HEAL, true);
					player.setEnableHeal(true);
					player.sendMessage("Heal mode enabled.");
				}
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_5"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			// Kiểm tra xem người chơi có party hay không
			if (player.getParty() == null)
			{
				player.sendMessage("You must be in a party to enable Recharge MP mode.");
			}
			else
			{
				if (player.isEnableRechargeMP())
				{
					player.getVariables().set(PlayerVariables.ENABLE_RECHARGE_MP, false);
					player.setEnableRechargeMP(false);
					player.sendMessage("Recharge MP mode disabled.");
				}
				else
				{
					player.getVariables().set(PlayerVariables.ENABLE_RECHARGE_MP, true);
					player.setEnableRechargeMP(true);
					player.sendMessage("Recharge MP mode enabled.");
				}
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_set_hp_threshold"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			try
			{
				int hpThreshold = Integer.parseInt(command.split(" ")[1]);
				if (hpThreshold < 0 || hpThreshold > 100)
				{
					player.sendMessage("HP threshold must be between 0 and 100.");
					return false;
				}
				if (player.getParty() == null) // Kiểm tra nếu không có party
				{
					player.sendMessage("You must be in a party to enable Heal mode.");
					return false; // Không cho phép bật chế độ
				}
				player.getAutoPlaySettings().setHpThreshold(hpThreshold); // Lưu cài đặt vào AutoPlaySettings
				player.sendMessage("HP threshold for leader set to: " + hpThreshold + "%");
			}
			catch (Exception e)
			{
				player.sendMessage("Invalid input. Please enter a number between 0 and 100.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		else if (command.startsWith("_bbstop_settings_menu_set_mp_threshold"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			try
			{
				int mpThreshold = Integer.parseInt(command.split(" ")[1]);
				if (mpThreshold < 0 || mpThreshold > 100)
				{
					player.sendMessage("MP threshold must be between 0 and 100.");
					return false;
				}
				if (player.getParty() == null) // Kiểm tra nếu không có party
				{
					player.sendMessage("You must be in a party to enable Heal mode.");
					return false; // Không cho phép bật chế độ
				}
				player.getAutoPlaySettings().setMpThreshold(mpThreshold); // Lưu cài đặt vào AutoPlaySettings
				player.sendMessage("MP threshold for leader set to: " + mpThreshold + "%");
			}
			catch (Exception e)
			{
				player.sendMessage("Invalid input. Please enter a number between 0 and 100.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (command.startsWith("_bbstop_settings_menu_autoplayer_6"))
		{
			String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/menu/autoplayer.htm");
			if (player.getParty() == null) // Kiểm tra nếu không có party
			{
				player.sendMessage("You must be in a party to enable Assist Leader mode.");
				return false; // Không cho phép bật chế độ
			}
			if (player.isEnableAssitPartyLeader())
			{
				player.getVariables().set(PlayerVariables.ENABLE_ASSIT_PARTY_LEADER, false);
				player.setEnableAssitPartyLeader(false);
				player.sendMessage("Assist Party Leader mode disabled.");
			}
			else
			{
				player.getVariables().set(PlayerVariables.ENABLE_ASSIT_PARTY_LEADER, true);
				player.setEnableAssitPartyLeader(true);
				player.sendMessage("Assist Party Leader mode enabled.");
			}
			dialog = replaceVars(player, dialog);
			player.sendPacket(new NpcHtmlMessage(dialog));
		}
		if (returnHtml != null)
		{
			// if (Config.CUSTOM_CB_ENABLED)
			// {
			// returnHtml = returnHtml.replace("%navigation%", navigation);
			// }
			CommunityBoardHandler.separateAndSend(returnHtml, player);
		}
		return false;
	}
	
	/**
	 * Gets the Favorite links for the given player.
	 * 
	 * @param player
	 *            the player
	 * @return the favorite links count
	 */
	private static int getFavoriteCount(Player player)
	{
		int count = 0;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement(COUNT_FAVORITES))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					count = rs.getInt("favorites");
				}
			}
		}
		catch (Exception e)
		{
			LOG.warning(FavoriteBoard.class.getSimpleName() + ": Coudn't load favorites count for " + player);
		}
		return count;
	}
	
	/**
	 * Gets the registered regions count for the given player.
	 * 
	 * @param player
	 *            the player
	 * @return the registered regions count
	 */
	private static int getRegionCount(Player player)
	{
		return 0; // TODO: Implement.
	}
	
	/**
	 * @param off
	 * @return
	 */
	private String online(boolean off)
	{
		int i = 0;
		int j = 0;
		for (Player player : World.getInstance().getPlayers())
		{
			i++;
			if (player.isInOfflineMode())
			{
				j++;
			}
		}
		return Util.formatAdena(!off ? (i + j) : j);
	}
	
	private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");
	
	public static String time()
	{
		return TIME_FORMAT.format(new Date(System.currentTimeMillis()));
	}
	
	/**
	 * @param player
	 * @return
	 */
	public static String getOnlineTime(Player player)
	{
		long total = player.getOnlineTime() + ((System.currentTimeMillis() / 1000) - player.getOnlineBeginTime());
		long days = (total / (60 * 60 * 24)) % 7;
		long hours = ((total - TimeUnit.DAYS.toSeconds(days)) / (60 * 60)) % 24;
		long minutes = (total - TimeUnit.DAYS.toSeconds(days) - TimeUnit.HOURS.toSeconds(hours)) / 60;
		if (days >= 1)
		{
			return days + " d. " + hours + " h. " + minutes + " min";
		}
		return hours + " hours " + player.getOnlineTime();
	}
	
	public String getServerRunTime()
	{
		int timeSeconds = GameTimeTaskManager.getInstance().getServerRunTime();
		String timeResult = "";
		if (timeSeconds >= 86400)
		{
			timeResult = Integer.toString(timeSeconds / 86400) + " Days " + Integer.toString((timeSeconds % 86400) / 3600) + " hours";
		}
		else
		{
			timeResult = Integer.toString(timeSeconds / 3600) + " Hours " + Integer.toString((timeSeconds % 3600) / 60) + " mins";
		}
		return timeResult;
	}
	
	private static void loadSingleBuffs()
	{
		allSingleBuffs = new LinkedList<>();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT * FROM npcbuffer_buff_list WHERE canUse = 1 ORDER BY Buff_Class ASC, id"); ResultSet rset = statement.executeQuery())
		{
			while (rset.next())
			{
				int id = rset.getInt("id");
				int buffClass = rset.getInt("buff_class");
				String buffType = rset.getString("buffType");
				int buffId = rset.getInt("buffId");
				int buffLevel = rset.getInt("buffLevel");
				int forClass = rset.getInt("forClass");
				boolean canUse = rset.getInt("canUse") == 1;
				allSingleBuffs.add(new SingleBuff(id, buffClass, buffType, buffId, buffLevel, forClass, canUse));
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Error while loading Single Buffs" + e);
		}
	}
	
	private static class SingleBuff
	{
		public final int	_buffClass;
		public final String	_buffType;
		public final int	_buffId;
		public final int	_buffLevel;
		public final String	_buffName;
		public int			_forClass;
		public boolean		_canUse;
		
		private SingleBuff(int id, int buffClass, String buffType, int buffId, int buffLevel, int forClass, boolean canUse)
		{
			_buffClass = buffClass;
			_buffType = buffType;
			_buffId = buffId;
			_buffLevel = buffLevel;
			_forClass = forClass;
			_canUse = canUse;
			_buffName = SkillData.getInstance().getSkill(buffId, buffLevel).getName();
		}
	}
	
	public static void loadSchemes(Player player, Connection con)
	{
		// Loading Scheme Templates
		try (PreparedStatement statement = con.prepareStatement("SELECT id, scheme_name, icon, total_lcoin_used FROM npcbuffer_scheme_list WHERE player_id=?"))
		{
			statement.setInt(1, player.getObjectId());
			try (ResultSet rset = statement.executeQuery())
			{
				while (rset.next())
				{
					int schemeId = rset.getInt("id");
					String schemeName = rset.getString("scheme_name");
					int iconId = rset.getInt("icon");
					int totalCostLcoin = rset.getInt("total_lcoin_used");
					player.getBuffSchemes().add(new PlayerScheme(schemeId, schemeName, iconId, totalCostLcoin));
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Error while loading Scheme Content of the Player" + e);
		}
		// Loading Scheme Contents
		for (PlayerScheme scheme : player.getBuffSchemes())
		{
			try (PreparedStatement statement = con.prepareStatement("SELECT skill_id, skill_level, buff_class, price FROM npcbuffer_scheme_contents WHERE scheme_id=?"))
			{
				statement.setInt(1, scheme.schemeId);
				try (ResultSet rset = statement.executeQuery())
				{
					while (rset.next())
					{
						int skillId = rset.getInt("skill_id");
						int skillLevel = rset.getInt("skill_level");
						int forClass = rset.getInt("buff_class");
						int price = rset.getInt("price"); // Load giá trị price từ database
						scheme.schemeBuffs.add(new SchemeBuff(skillId, skillLevel, forClass, price));
					}
				}
			}
			catch (SQLException e)
			{
				LOGGER.warning("Error while loading Scheme Content of the Player" + e);
			}
		}
	}
	
	private static String main(final Player player)
	{
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_main.htm");
		final String bottonA, bottonB, bottonC;
		if (isPetBuff(player))
		{
			bottonA = "Auto Buff Pet";
			bottonB = "Heal My Pet";
			bottonC = "Remove Pet Buffs";
			dialog = dialog.replace("%topbtn%", "<button value=\" " + (player.getPet() != null ? player.getPet().getName() : "You don't have Pet") + "\" action=\"bypass _bbsbufferbypass buffpet 0 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		else
		{
			bottonA = "Auto Buff";
			bottonB = "Heal";
			bottonC = "Remove Buffs";
			dialog = dialog.replace("%topbtn%", "<button value=" + player.getName() + " action=\"bypass _bbsbufferbypass buffpet 1 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		boolean useLcoins = player.isUsingLcoins();
		String lcoinColor = useLcoins ? "99BBFF" : "FFFFFF";
		String debtColor = useLcoins ? "FFFFFF" : "FF9999";
		// Lấy số lượng Lcoins mà người chơi có từ inventory
		int lcoinAmount = player.getLcoinsAmount(); // Lấy giá trị từ inventory
		// Tải giá trị debt từ cơ sở dữ liệu (nếu chưa tải)
		player.loadDebtAmount(player.getObjectId());
		dialog = dialog.replace("%lcoin_amount%", "<font name=\"hs12\" color=\"" + lcoinColor + "\">" + "-->" + lcoinAmount + "<--" + "</font>");
		dialog = dialog.replace("%debt_amount%", "<font name=\"hs12\" color=\"" + debtColor + "\">" + "-->" + player.getDebtAmount() + "<--" + "</font>");
		// Thêm nút chuyển đổi thanh toán
		// dialog = dialog.replace("%toggle_payment%", "<button value=\"Toggle Payment Mode\" action=\"bypass _bbsbufferbypass toggle_payment_mode 0 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		if (ENABLE_BUFF_SET)
		{
			dialog = dialog.replace("%autobuff%", "<button value=\" " + bottonA + "\" action=\"bypass _bbsbufferbypass castBuffSet 0 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		if (ENABLE_HEAL)
		{
			dialog = dialog.replace("%heal%", "<button value=\" " + bottonB + "\" action=\"bypass _bbsbufferbypass heal 0 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		if (ENABLE_BUFF_REMOVE)
		{
			dialog = dialog.replace("%removebuffs%", "<button value=\" " + bottonC + "\" action=\"bypass _bbsbufferbypass removeBuffs 0 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		if (ENABLE_SCHEME_SYSTEM)
		{
			dialog = dialog.replace("%schemePart%", generateScheme(player));
		}
		if (player.isGM())
		{
			dialog = dialog.replace("%gm%", "<button value=\"Manage Schemes\" action=\"bypass _bbsbufferbypass redirect manage_buffs 0 0\" width=135 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\">");
		}
		else
		{
			dialog = dialog.replace("%gm%", "");
		}

		// Cập nhật hiển thị chi phí dựa trên level người chơi
		if (player.getLevel() < 40)
		{
			// Hiển thị "Free" cho người chơi dưới level 40
			dialog = dialog.replace("Price: 50_000 Adena", "<font color=00FF00>Free</font>");
			dialog = dialog.replace("Price: 200_000 Adena", "<font color=00FF00>Free</font>");
			dialog = dialog.replace("Price: 190_000 Adena", "<font color=00FF00>Free</font>");
			dialog = dialog.replace("Items Required: Adena", "Items Required: <font color=00FF00>Free</font>");
		}
		else
		{
			// Hiển thị chi phí bình thường cho người chơi từ level 40 trở lên
			dialog = dialog.replace("Price: 50_000 Adena", "Price: " + HEAL_PRICE + " Adena");
			dialog = dialog.replace("Price: 200_000 Adena", "Price: 200_000 Adena");
			dialog = dialog.replace("Price: 190_000 Adena", "Price: 190_000 Adena");
		}

		dialog = dialog.replace("\r\n", "");
		dialog = dialog.replace("\t", "");
		return dialog;
	}
	
	private static void setPetBuff(Player player, String eventParam2)
	{
		player.getVariables().set("SchemeBufferPet", Integer.valueOf(eventParam2));
	}
	
	private static boolean isPetBuff(Player player)
	{
		int value = player.getVariables().getInt("SchemeBufferPet", 0);
		return value > 0;
	}
	
	private static String generateScheme(Player player)
	{
		StringBuilder mainBuilder = new StringBuilder();
		// Giao diện tạo scheme mới
		mainBuilder.append("<tr>");
		mainBuilder.append("<td width=240 height=30 valign=top align=center>");
		mainBuilder.append("<table border=0 width=240 height=40 cellspacing=4 cellpadding=3 bgcolor=10100E>");
		mainBuilder.append("<tr>");
		mainBuilder.append("<td align=right valign=top>");
		mainBuilder.append("<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=Icon.skill1510>");
		mainBuilder.append("<tr>");
		mainBuilder.append("<td width=32 height=32 align=center valign=top>");
		mainBuilder.append("<button value=\" \" action=\"bypass _bbsbufferbypass create_1 0 x x\" width=34 height=34 back=L2UI_CT1.ItemWindow_DF_Frame_Down fore=L2UI_CT1.ItemWindow_DF_Frame />");
		mainBuilder.append("</td>");
		mainBuilder.append("</tr>");
		mainBuilder.append("</table>");
		mainBuilder.append("</td>");
		mainBuilder.append("<td width=150 valign=top>");
		mainBuilder.append("<font name=hs12 color=ADA71B>New Scheme</font><br1>");
		mainBuilder.append("<font color=FFFFFF name=__SYSTEMWORLDFONT>Free of Choice</font>");
		mainBuilder.append("</td>");
		mainBuilder.append("</tr>");
		mainBuilder.append("</table>");
		mainBuilder.append("<br>");
		mainBuilder.append("</td>");
		mainBuilder.append("</tr>");
		// Lấy danh sách scheme của người chơi
		final Iterator<PlayerScheme> it = player.getBuffSchemes().iterator();
		for (int i = 0; i < SCHEMES_PER_PLAYER; i++)
		{
			if (it.hasNext())
			{
				final PlayerScheme scheme = it.next();
				// Đồng bộ totalCostLcoin từ cơ sở dữ liệu để đảm bảo tính chính xác
				int totalCostLcoin = 0;
				try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT total_lcoin_used FROM npcbuffer_scheme_list WHERE id = ?"))
				{
					statement.setInt(1, scheme.schemeId);
					try (ResultSet rset = statement.executeQuery())
					{
						if (rset.next())
						{
							totalCostLcoin = rset.getInt("total_lcoin_used");
							scheme.totalCostLcoin = totalCostLcoin; // Cập nhật lại scheme trong bộ nhớ
						}
					}
				}
				catch (SQLException e)
				{
					LOGGER.warning("Error while fetching total Lcoin cost for scheme: " + e);
				}
				// Hiển thị scheme trên giao diện
				mainBuilder.append("<tr>");
				mainBuilder.append("<td width=240 height=30 valign=top align=center>");
				mainBuilder.append("<table border=0 width=240 height=40 cellspacing=4 cellpadding=3 bgcolor=10100E>");
				mainBuilder.append("<tr>");
				mainBuilder.append("<td align=right valign=top>");
				mainBuilder.append("<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=" + SCHEME_ICONS[scheme.iconId] + ">");
				mainBuilder.append("<tr>");
				mainBuilder.append("<td width=32 height=32 align=center valign=top>");
				mainBuilder.append("<button value=\" \" action=\"bypass _bbsbufferbypass cast " + scheme.schemeId + " x x\" width=34 height=34 back=L2UI_CT1.ItemWindow_DF_Frame_Down fore=L2UI_CT1.ItemWindow_DF_Frame />");
				mainBuilder.append("</td>");
				mainBuilder.append("</tr>");
				mainBuilder.append("</table>");
				mainBuilder.append("</td>");
				mainBuilder.append("<td width=120 valign=center>");
				mainBuilder.append("<font name=hs12 color=ADA71B>" + scheme.schemeName + "</font><br1>");
				// Tính chi phí thực tế dựa trên level hiện tại
				int actualCost = 0;
				if (player.getLevel() >= 40)
				{
					for (SchemeBuff buff : scheme.schemeBuffs)
					{
						actualCost += costSchemeBuff(buff.skillId);
					}
				}
				String costDisplay = (player.getLevel() < 40) ? "Free" : actualCost + " Adena";
				mainBuilder.append("<font color=FFFFFF name=__SYSTEMWORLDFONT>Total Cost: " + costDisplay + "</font>");
				mainBuilder.append("</td>");
				mainBuilder.append("<td width=30 align=center>");
				mainBuilder.append("<br>");
				mainBuilder.append("<button value=\" \" action=\"bypass _bbsbufferbypass manage_scheme_select " + scheme.schemeId + " x x\" width=32 height=32 back=L2UI_CT1.RadarMap_DF_OptionBtn_Down fore=L2UI_CT1.RadarMap_DF_OptionBtn />");
				mainBuilder.append("</td>");
				mainBuilder.append("</tr>");
				mainBuilder.append("</table>");
				mainBuilder.append("<br>");
				mainBuilder.append("</td>");
				mainBuilder.append("</tr>");
			}
			else
			{
				// Trường hợp không còn scheme nào
				mainBuilder.append("<tr>");
				mainBuilder.append("<td width=240 height=50 valign=top align=center></td>");
				mainBuilder.append("</tr>");
			}
		}
		return mainBuilder.toString();
	}
	
	private static String viewAllSchemeBuffs(Player player, String scheme, String page)
	{
		int pageN = Integer.parseInt(page);
		int schemeId = Integer.parseInt(scheme);
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_buffs.htm");
		int[] buffCount = getBuffCount(player, schemeId);
		int TOTAL_BUFF = buffCount[0];
		int BUFF_COUNT = buffCount[1];
		int DANCE_SONG = buffCount[2];
		if (isPetBuff(player))
		{
			dialog = dialog.replace("%topbtn%", (player.getPet() != null ? player.getPet().getName() : "You don't have Pet"));
		}
		else
		{
			dialog = dialog.replace("%topbtn%", player.getName());
		}
		// Buff count
		dialog = dialog.replace("%bcount%", String.valueOf(MAX_SCHEME_BUFFS - BUFF_COUNT));
		dialog = dialog.replace("%dscount%", String.valueOf(MAX_SCHEME_DANCES - DANCE_SONG));
		// Tính tổng chi phí L-Coins cho scheme hiện tại (LUÔN dựa trên level hiện tại)
		int totalLCoins = 0;
		if (player.getLevel() < 40)
		{
			// Miễn phí cho người chơi dưới level 40
			totalLCoins = 0;
		}
		else
		{
			// Tính lại chi phí dựa trên level hiện tại
			for (SchemeBuff buff : player.getBuffSchemeById(schemeId).schemeBuffs)
			{
				totalLCoins += costSchemeBuff(buff.skillId); // Tính lại chi phí thực tế
			}
		}
		dialog = dialog.replace("%total_lcoin%", String.valueOf(totalLCoins));
		// Current selected buffs
		final List<SchemeBuff> schemeBuffs = new ArrayList<>();
		final List<SchemeBuff> schemeDances = new ArrayList<>();
		for (SchemeBuff buff : player.getBuffSchemeById(schemeId).schemeBuffs)
		{
			switch (getBuffType(buff.skillId))
			{
				case "song":
				case "dance":
					schemeDances.add(buff);
					break;
				default:
					schemeBuffs.add(buff);
					break;
			}
		}
		final int MAX_ROW_SIZE = 16;
		final int[] ROW_SIZES = new int[]
		{
			12,
			12 + 16,
			12 + 16 + 12
		};
		final StringBuilder addedBuffs = new StringBuilder();
		int row = 0;
		for (int i = 0; i < ROW_SIZES[2]; i++)
		{
			// Open row
			if ((i == 0) || (((i + 1) - ROW_SIZES[Math.max(row - 1, 0)]) == 1))
			{
				addedBuffs.append("<tr>");
			}
			if ((row < 2) && (schemeBuffs.size() > i))
			{
				final Skill skill = SkillData.getInstance().getSkill(schemeBuffs.get(i).skillId, schemeBuffs.get(i).skillLevel);
				String iconSkill = skill.getIcon();
				if (skill.getId() == 45108)
				{
					// Magic Barrier
					iconSkill = "icon.skill1036";
				}
				else if (skill.getId() == 45113)
				{
					// Clarity
					iconSkill = "icon.skill1397";
				}
				else if (skill.getId() == 45176)
				{
					// Recover HP
					iconSkill = "icon.skill0211";
				}
				else if (skill.getId() == 45177)
				{
					// Recover MP
					iconSkill = "icon.skill0213";
				}
				else if (skill.getId() == 45236)
				{
					// Prophecy of Light
					iconSkill = "icon.skill11784";
				}
				else if (skill.getId() == 1507)
				{
					// Spirit of Shillien
					iconSkill = "icon.skill1912";
				}
				else if (skill.getId() == 1388)
				{
					// Greater Might
					iconSkill = "icon.skill1388";
				}
				else if (skill.getId() == 1389)
				{
					// Greater Shield
					iconSkill = "icon.skill1389";
				}
				addedBuffs.append("<td width=34>");
				addedBuffs.append("<table cellspacing=0 cellpadding=0 width=34 height=34 background=" + iconSkill + ">");
				addedBuffs.append("<tr>");
				addedBuffs.append("<td width=34>");
				addedBuffs.append("<button value=\" \" action=\"bypass _bbsbufferbypass remove_buff " + schemeId + "_" + skill.getId() + "_" + skill.getLevel() + " " + pageN + " x\" width=34 height=34 back=\"L2UI_CT1.ItemWindow_DF_Frame_Down\" fore=\"L2UI_CT1.ItemWindow_DF_Frame\"/>");
				addedBuffs.append("</td>");
				addedBuffs.append("</tr>");
				addedBuffs.append("</table>");
				addedBuffs.append("</td>");
			}
			else if ((row >= 2) && (schemeDances.size() > (i - ROW_SIZES[row - 1])))
			{
				final Skill skill = SkillData.getInstance().getSkill(schemeDances.get(i - ROW_SIZES[row - 1]).skillId, schemeDances.get(i - ROW_SIZES[row - 1]).skillLevel);
				String iconSkill = skill.getIcon();
				if (skill.getId() == 45108)
				{
					// Magic Barrier
					iconSkill = "icon.skill1036";
				}
				else if (skill.getId() == 45113)
				{
					// Clarity
					iconSkill = "icon.skill1397";
				}
				else if (skill.getId() == 45176)
				{
					// Recover HP
					iconSkill = "icon.skill0211";
				}
				else if (skill.getId() == 45177)
				{
					// Recover MP
					iconSkill = "icon.skill0213";
				}
				else if (skill.getId() == 45236)
				{
					// Prophecy of Light
					iconSkill = "icon.skill11784";
				}
				else if (skill.getId() == 1507)
				{
					// Spirit of Shillien
					iconSkill = "icon.skill1912";
				}
				else if (skill.getId() == 1388)
				{
					// Greater Might
					iconSkill = "icon.skill1388";
				}
				else if (skill.getId() == 1389)
				{
					// Greater Shield
					iconSkill = "icon.skill1389";
				}
				addedBuffs.append("<td width=34>");
				addedBuffs.append("<table cellspacing=0 cellpadding=0 width=34 height=34 background=" + iconSkill + ">");
				addedBuffs.append("<tr>");
				addedBuffs.append("<td width=34>");
				addedBuffs.append("<button value=\" \" action=\"bypass _bbsbufferbypass remove_buff " + schemeId + "_" + skill.getId() + "_" + skill.getLevel() + " " + pageN + " x\" width=34 height=34 back=\"L2UI_CT1.ItemWindow_DF_Frame_Down\" fore=\"L2UI_CT1.ItemWindow_DF_Frame\"/>");
				addedBuffs.append("</td>");
				addedBuffs.append("</tr>");
				addedBuffs.append("</table>");
				addedBuffs.append("</td>");
			}
			else
			{
				addedBuffs.append("<td width=34>");
				addedBuffs.append("<table cellspacing=0 cellpadding=0 width=34 height=34 background=L2UI_CH3.multisell_plusicon>");
				addedBuffs.append("<tr>");
				addedBuffs.append("<td width=34>");
				addedBuffs.append("&nbsp;");
				addedBuffs.append("</td>");
				addedBuffs.append("</tr>");
				addedBuffs.append("</table>");
				addedBuffs.append("</td>");
			}
			if ((ROW_SIZES[row] < (MAX_ROW_SIZE * (row + 1))) && ((i + 1) > ROW_SIZES[row]))
			{
				for (int z = ROW_SIZES[row]; z < (MAX_ROW_SIZE * (row + 1)); z++)
				{
					addedBuffs.append("<td width=1>");
					addedBuffs.append("&nbsp;");
					addedBuffs.append("</td>");
				}
			}
			// Close row
			if (((i + 1) - ROW_SIZES[row]) == 0)
			{
				addedBuffs.append("</tr>");
				row++;
			}
		}
		// Current available buffs to add
		final List<Skill> availableSkills = new ArrayList<>();
		for (SingleBuff singleBuff : allSingleBuffs)
		{
			if (!singleBuff._canUse)
			{
				continue;
			}
			// Check if we already added this buff
			boolean hasAddedThisBuff = false;
			for (SchemeBuff buff : schemeBuffs)
			{
				if (buff.skillId == singleBuff._buffId)
				{
					hasAddedThisBuff = true;
					break;
				}
			}
			for (SchemeBuff buff : schemeDances)
			{
				if (buff.skillId == singleBuff._buffId)
				{
					hasAddedThisBuff = true;
					break;
				}
			}
			if (hasAddedThisBuff)
			{
				continue;
			}
			// If we reached the limit dont add dances or buffs
			switch (singleBuff._buffType)
			{
				case "song":
				case "dance":
					if (DANCE_SONG >= MAX_SCHEME_DANCES)
					{
						continue;
					}
					break;
				default:
					if (BUFF_COUNT >= MAX_SCHEME_BUFFS)
					{
						continue;
					}
					break;
			}
			availableSkills.add(SkillData.getInstance().getSkill(singleBuff._buffId, singleBuff._buffLevel));
		}
		final int SKILLS_PER_ROW = 4;
		final int MAX_SKILLS_ROWS = 3;
		final StringBuilder availableBuffs = new StringBuilder();
		final int maxPage = (int) Math.ceil(((double) availableSkills.size() / (SKILLS_PER_ROW * MAX_SKILLS_ROWS)) - 1);
		final int currentPage = Math.max(Math.min(maxPage, pageN), 0);
		final int startIndex = currentPage * SKILLS_PER_ROW * MAX_SKILLS_ROWS;
		for (int i = startIndex; i < (startIndex + (SKILLS_PER_ROW * MAX_SKILLS_ROWS)); i++)
		{
			// Open row
			if ((i == 0) || ((i % SKILLS_PER_ROW) == 0))
			{
				availableBuffs.append("<tr>");
			}
			if (availableSkills.size() > i)
			{
				final Skill skill = availableSkills.get(i);
				availableBuffs.append("<td>");
				availableBuffs.append("<table cellspacing=2 cellpadding=2 width=140 height=40 bgcolor=000000>");
				availableBuffs.append("<tr>");
				availableBuffs.append("<td>");
				String iconSkill = skill.getIcon();
				if (skill.getId() == 45108)
				{
					// Magic Barrier
					iconSkill = "icon.skill1036";
				}
				else if (skill.getId() == 45113)
				{
					// Clarity
					iconSkill = "icon.skill1397";
				}
				else if (skill.getId() == 45176)
				{
					// Recover HP
					iconSkill = "icon.skill0211";
				}
				else if (skill.getId() == 45177)
				{
					// Recover MP
					iconSkill = "icon.skill0213";
				}
				else if (skill.getId() == 45236)
				{
					// Prophecy of Light
					iconSkill = "icon.skill11784";
				}
				else if (skill.getId() == 1507)
				{
					// Spirit of Shillien
					iconSkill = "icon.skill1912";
				}
				else if (skill.getId() == 1388)
				{
					// Greater Might
					iconSkill = "icon.skill1388";
				}
				else if (skill.getId() == 1389)
				{
					// Greater Shield
					iconSkill = "icon.skill1389";
				}
				availableBuffs.append("<table border=0 cellspacing=0 cellpadding=0 width=34 height=34 background=" + iconSkill + ">");
				availableBuffs.append("<tr>");
				availableBuffs.append("<td>");
				availableBuffs.append("<table cellspacing=0 cellpadding=0 width=34 height=34 background=L2UI.item_click>");
				availableBuffs.append("<tr>");
				availableBuffs.append("<td>");
				availableBuffs.append("<br>");
				availableBuffs.append("</td>");
				availableBuffs.append("<td height=34>");
				availableBuffs.append("<button value=\" \" action=\"bypass -h _bbsbufferbypass add_buff ").append(scheme).append("_").append(skill.getId()).append("_").append(skill.getLevel()).append(" ").append(currentPage).append(" ").append(TOTAL_BUFF).append("\" width=34 height=34 back=\"L2UI_CT1.ItemWindow_DF_Frame_Down\" fore=\"L2UI_CT1.ItemWindow_DF_Frame\">");
				availableBuffs.append("</td>");
				availableBuffs.append("</tr>");
				availableBuffs.append("</table>");
				availableBuffs.append("</td>");
				availableBuffs.append("</tr>");
				availableBuffs.append("</table>");
				availableBuffs.append("</td>");
				availableBuffs.append("<td width=120 align=center>");
				// Hiển thị chi phí dựa trên level người chơi
				if (player.getLevel() < 40)
				{
					availableBuffs.append("<font name=CREDITTEXTSMALL>" + skill.getName() + " <font color=00FF00>Free</font></font>");
				}
				else
				{
					availableBuffs.append("<font name=CREDITTEXTSMALL>" + skill.getName() + " " + costSchemeBuff(skill.getId()) + " Adena</font>");
				}
				availableBuffs.append("</td>");
				availableBuffs.append("</tr>");
				availableBuffs.append("</table>");
				availableBuffs.append("</td>");
			}
			else
			{
				availableBuffs.append("<td>");
				availableBuffs.append("<table cellspacing=2 cellpadding=2 width=140 height=40>");
				availableBuffs.append("<tr>");
				availableBuffs.append("<td>");
				availableBuffs.append("&nbsp;");
				availableBuffs.append("</td>");
				availableBuffs.append("</tr>");
				availableBuffs.append("</table>");
				availableBuffs.append("</td>");
			}
			// Close row
			if ((((i + 1) % SKILLS_PER_ROW) == 0) || ((i - startIndex) >= (SKILLS_PER_ROW * MAX_SKILLS_ROWS)))
			{
				availableBuffs.append("</tr>");
			}
		}
		dialog = dialog.replace("%scheme%", scheme);
		dialog = dialog.replace("%addedBuffs%", addedBuffs.toString());
		dialog = dialog.replace("%availableBuffs%", availableBuffs.toString());
		dialog = dialog.replace("%prevPage%", (currentPage > 0 ? "bypass _bbsbufferbypass manage_scheme_1 " + scheme + " " + (currentPage - 1) + " x" : ""));
		dialog = dialog.replace("%nextPage%", (currentPage < maxPage ? "bypass _bbsbufferbypass manage_scheme_1 " + scheme + " " + (currentPage + 1) + " x" : ""));
		dialog = dialog.replace("\r\n", "");
		dialog = dialog.replace("\t", "");
		return dialog;
	}
	
	private static boolean canHeal(Player player)
	{
		if (!checkConditions(player) || !player.isInsideZone(ZoneId.PEACE))
		{
			return false;
		}
		return true;
	}
	
	private static void heal(Player player, boolean isPet)
	{
		if (!canHeal(player))
		{
			return;
		}
		if (!isPet)
		{
			player.setCurrentHp(player.getMaxHp(), false);
			player.setCurrentMp(player.getMaxMp());
			player.setCurrentCp(player.getMaxCp());
			player.broadcastSkillOrSocialAnimation(22217, 1, 0, 0);
		}
		else if (player.getPet() != null)
		{
			for (Summon summon : player.getServitors().values())
			{
				summon.setCurrentHp(summon.getMaxHp());
				summon.setCurrentMp(summon.getMaxMp());
				summon.setCurrentCp(summon.getMaxCp());
				summon.broadcastPacket(new MagicSkillUse(summon, 22217, 1, 0, 0));
			}
		}
	}
	
	private static boolean checkConditions(Player player)
	{
		String msg = null;
		if (player.isInOlympiadMode() || OlympiadManager.getInstance().isRegistered(player))
		{
			msg = "You cannot receive buffs while registered in the Grand Olympiad.";
		}
		else if ((player.getLevel() >= 10) && (((player.getPvpFlag() > 0) && !player.isInsideZone(ZoneId.PEACE)) || player.isInCombat()))
		{
			msg = "You cannot receive buffs while in combat.";
		}
		else if (!BUFF_WITH_KARMA && (player.getReputation() < 0))
		{
			msg = "Chaotic players may not use the buffer.";
		}
		else if (OlympiadManager.getInstance().isRegisteredInComp(player))
		{
			msg = "You cannot use the buffer while participating in the Grand Olympiad.";
		}
		else if (OlympiadManager.getInstance().isRegistered(player))
		{
			msg = "You cannot use the buffer while participating in the Grand Olympiad.";
		}
		else if (player.getLevel() < MIN_LEVEL)
		{
			msg = "Your level is too low. You have to be at least level " + MIN_LEVEL + ", to use the buffer.";
		}
		else if ((player.getPvpFlag() > 0) && !Config.SCHEME_ALLOW_FLAG)
		{
			msg = "You cannot receive buffs while flagged. Please, try again later.";
		}
		else if (player.isInCombat() && !Config.SCHEME_ALLOW_FLAG)
		{
			msg = "You cannot receive buffs while in combat.<br>Please, try again later.";
		}
		// Ady - Block time that the player cannot use the community buffer
		// else if (player.getResurrectionBuffBlockedTime() > System.currentTimeMillis())
		// {
		// msg = "You must wait 10 seconds after being resurrected to use the buffer.";
		// }
		if (msg == null)
		{
			return true;
		}
		sendErrorMessageToPlayer(player, msg);
		CommunityBoardHandler.separateAndSend(main(player), player);
		return false;
	}
	
	private static void sendErrorMessageToPlayer(Player player, String msg)
	{
		player.sendPacket(new CreatureSay(player, ChatType.CRITICAL_ANNOUNCE, "Error", msg));
	}
	
	private static void deleteScheme(int eventParam2, Player player)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement statement = con.prepareStatement("DELETE FROM npcbuffer_scheme_list WHERE id=? LIMIT 1"))
			{
				statement.setString(1, String.valueOf(eventParam2));
				statement.executeUpdate();
			}
			try (PreparedStatement statement = con.prepareStatement("DELETE FROM npcbuffer_scheme_contents WHERE scheme_id=?"))
			{
				statement.setString(1, String.valueOf(eventParam2));
				statement.executeUpdate();
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Error while deleting Scheme Content" + e);
		}
		int realId = eventParam2;
		for (PlayerScheme scheme : player.getBuffSchemes())
		{
			if (scheme.schemeId == realId)
			{
				player.getBuffSchemes().remove(scheme);
				break;
			}
		}
	}
	
	private static class BackHp implements Runnable
	{
		private final Playable	playable;
		private final double	hp;
		private final double	mp;
		private final double	cp;
		
		private BackHp(Playable playable, double hp, double mp, double cp)
		{
			this.playable = playable;
			this.hp = hp;
			this.mp = mp;
			this.cp = cp;
		}
		
		@Override
		public void run()
		{
			playable.getActingPlayer().getVariables().remove("BackHpOn");
			playable.setCurrentHp(hp, false);
			playable.setCurrentMp(mp);
			playable.setCurrentCp(cp);
		}
	}
	
	private static String viewAllBuffTypes()
	{
		StringBuilder builder = new StringBuilder();
		builder.append("<html><head><title>").append(TITLE_NAME).append("</title></head><body><br><center><img src=\"L2UI_CH3.herotower_deco\" width=256 height=32><br>");
		builder.append("<font color=LEVEL>[Buff management]</font><br>");
		if (ENABLE_BUFFS)
		{
			builder.append("<button value=\"Buffs\" action=\"bypass _bbsbufferbypass edit_buff_list buff Buffs 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		}
		if (ENABLE_SPECIAL)
		{
			builder.append("<button value=\"Special Buffs\" action=\"bypass _bbsbufferbypass edit_buff_list special Special_Buffs 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		}
		if (ENABLE_SONGS)
		{
			builder.append("<button value=\"Songs\" action=\"bypass _bbsbufferbypass edit_buff_list song Songs 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		}
		if (ENABLE_DANCES)
		{
			builder.append("<button value=\"Dances\" action=\"bypass _bbsbufferbypass edit_buff_list dance Dances 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		}
		if (ENABLE_BUFF_SET)
		{
			builder.append("<button value=\"Buff Sets\" action=\"bypass _bbsbufferbypass edit_buff_list set Buff_Sets 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"><br>");
		}
		// if (ENABLE_RESIST)
		// {
		// builder.append("<button value=\"Resist Buffs\" action=\"bypass _bbsbufferbypass edit_buff_list resist Resists 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		// }
		// if (ENABLE_CHANTS)
		// {
		// builder.append("<button value=\"Chants\" action=\"bypass _bbsbufferbypass edit_buff_list chant Chants 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		// }
		// if (ENABLE_OTHERS)
		// {
		// builder.append("<button value=\"Others Buffs\" action=\"bypass _bbsbufferbypass edit_buff_list others Others_Buffs 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		// }
		// if (ENABLE_CUBIC)
		// {
		// builder.append("<button value=\"Cubics\" action=\"bypass _bbsbufferbypass edit_buff_list cubic cubic_Buffs 1\" width=200 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
		// }
		builder.append("<button value=\"Back\" action=\"bypass _bbsbufferbypass redirect main 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"></center>");
		builder.append("</body></html>");
		return builder.toString();
	}
	
	private static String getDeleteSchemePage(Player player)
	{
		StringBuilder builder = new StringBuilder();
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_delete.htm");
		// builder.append("<html><head><title>").append(TITLE_NAME).append("</title></head><body><br><center><img src=\"L2UI_CH3.herotower_deco\" width=256 height=32><br><font name=\"hs12\" color=LEVEL>Available schemes:</font><br><br>");
		for (PlayerScheme scheme : player.getBuffSchemes())
		{
			builder.append("<button value=\" ").append(scheme.schemeName).append("\" action=\"bypass _bbsbufferbypass delete ").append(scheme.schemeId).append(" ").append(scheme.schemeName).append(" x\" width=200 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"><br1>");
		}
		// builder.append("<br><button value=\"Back\" action=\"bypass _bbsbufferbypass redirect main 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"></center>");
		dialog = dialog.replace("%schemes%", builder.toString());
		return dialog;
	}
	
	private static String getItemNameHtml(Player st, int itemval)
	{
		return "&#" + itemval + ";";
	}
	
	private static String buildHtml(String buffType, Player player)
	{
		StringBuilder builder = new StringBuilder();
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_indbuffs.htm");
		// builder.append("<html><head><title>").append(TITLE_NAME).append("</title></head><body><br><center><br>");
		Collection<String> availableBuffs = new ArrayList<>();
		for (SingleBuff buff : allSingleBuffs)
		{
			if (!buff._canUse)
			{
				continue;
			}
			if (buff._buffType.equals(buffType))
			{
				String bName = SkillData.getInstance().getSkill(buff._buffId, buff._buffLevel).getName();
				bName = bName.replace(" ", "+");
				availableBuffs.add(bName + "_" + buff._buffId + "_" + buff._buffLevel);
			}
		}
		if (availableBuffs.isEmpty())
		{
			builder.append("There are no available buffs at the moment.");
		}
		else
		{
			// builder.append("<font name=\"hs12\" color=LEVEL>Buffer</font>");
			builder.append("<BR1><table width=650>");
			int index = 0;
			for (String buff : availableBuffs)
			{
				if ((index % 2) == 0)
				{
					if (index > 0)
					{
						builder.append("</tr>");
					}
					builder.append("<tr>");
				}
				buff = buff.replace("_", " ");
				String[] buffSplit = buff.split(" ");
				String name = buffSplit[0];
				int id = Integer.parseInt(buffSplit[1]);
				int level = Integer.parseInt(buffSplit[2]);
				name = name.replace("+", " ");
				// Hiển thị chi phí dựa trên level người chơi - giữ nguyên layout gốc
				String buttonText;
				if (player.getLevel() < 40)
				{
					buttonText = name + " (Free)";
				}
				else
				{
					int cost = costDirectBuff(id);
					buttonText = name + " (" + cost + " Adena)";
				}
				builder.append("<td align=center><table cellspacing=0 cellpadding=0><tr><td align=right>").append(getSkillIconHtml(id, level)).append("</td><td><button value=\" ").append(buttonText).append("\" action=\"bypass _bbsbufferbypass giveBuffs ").append(id).append(" ").append(level).append(" ").append(buffType).append("\" width=190 height=32 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td><td align=left>").append(getSkillIconHtml(id, level)).append("</td></tr></table></td>");
				index++;
			}
			builder.append("</tr>");
			builder.append("</table>");
		}
		// builder.append("<br><center><button value=\"Back\" action=\"bypass _bbsbufferbypass redirect main 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"></center>");
		dialog = dialog.replace("%buffs%", builder.toString());
		return dialog;
	}
	
	private static String getEditSchemePage(Player player)
	{
		StringBuilder builder = new StringBuilder();
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_menu.htm");
		// builder.append("<html><head><title>").append(TITLE_NAME).append("</title></head><body><br><center><img src=\"L2UI_CH3.herotower_deco\" width=256 height=32><br><font name=\"hs12\" color=LEVEL>Select a scheme that you would like to manage:</font><br><br>");
		for (PlayerScheme scheme : player.getBuffSchemes())
		{
			builder.append("<button value=\" ").append(scheme.schemeName).append("\" action=\"bypass _bbsbufferbypass manage_scheme_select ").append(scheme.schemeId).append(" x x\" width=200 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"><br1>");
		}
		// builder.append("<br><button value=\"Back\" action=\"bypass _bbsbufferbypass redirect main 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"></center>");
		dialog = dialog.replace("%schemes%", builder.toString());
		return dialog;
	}
	
	private static int[] getBuffCount(Player player, int schemeId)
	{
		int count = 0;
		int D_S_Count = 0;
		int B_Count = 0;
		for (SchemeBuff buff : player.getBuffSchemeById(schemeId).schemeBuffs)
		{
			++count;
			int val = buff.forClass;
			if ((val == 1) || (val == 2))
			{
				++D_S_Count;
			}
			else
			{
				if ((player.getVipTier() < 2) && getBuffType(buff.skillId).equals("special"))
				{
					B_Count = 0;
				}
				else
				{
					++B_Count;
				}
			}
		}
		return new int[]
		{
			count,
			B_Count,
			D_S_Count
		};
	}
	
	private static String getOptionList(Player player, int schemeId)
	{
		final PlayerScheme scheme = player.getBuffSchemeById(schemeId);
		int[] buffCount = getBuffCount(player, schemeId);
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_options.htm");
		if (isPetBuff(player))
		{
			dialog = dialog.replace("%topbtn%", (player.getPet() != null ? player.getPet().getName() : "You don't have Pet"));
		}
		else
		{
			dialog = dialog.replace("%topbtn%", player.getName());
		}
		dialog = dialog.replace("%name%", (scheme != null ? scheme.schemeName : ""));
		dialog = dialog.replace("%bcount%", String.valueOf(buffCount[1]));
		dialog = dialog.replace("%dscount%", String.valueOf(buffCount[2]));
		dialog = dialog.replace("%manageBuffs%", "bypass _bbsbufferbypass manage_scheme_1 " + schemeId + " 0 x");
		dialog = dialog.replace("%changeName%", "bypass _bbsbufferbypass changeName_1 " + schemeId + " x x");
		dialog = dialog.replace("%changeIcon%", "bypass _bbsbufferbypass changeIcon_1 " + schemeId + " x x");
		dialog = dialog.replace("%deleteScheme%", "bypass _bbsbufferbypass delete " + schemeId + " x x");
		return dialog;
	}
	
	private static String getSkillIconHtml(int id, int level)
	{
		String iconNumber = getSkillIconNumber(id, level);
		return "<img width=32 height=32 src=\"Icon.skill" + iconNumber + "\">";
	}
	
	private static String getSkillIconNumber(int id, int level)
	{
		String formato;
		if (id == 4)
		{
			formato = "0004";
		}
		else if ((id > 9) && (id < 100))
		{
			formato = "00" + id;
		}
		else if ((id > 99) && (id < 1000))
		{
			formato = "0" + id;
		}
		else if (id == 1517)
		{
			formato = "1536";
		}
		else if (id == 1518)
		{
			formato = "1537";
		}
		else if (id == 1547)
		{
			formato = "0065";
		}
		else if (id == 2076)
		{
			formato = "0195";
		}
		else if ((id > 4550) && (id < 4555))
		{
			formato = "5739";
		}
		else if ((id > 4698) && (id < 4701))
		{
			formato = "1331";
		}
		else if ((id > 4701) && (id < 4704))
		{
			formato = "1332";
		}
		else if (id == 6049)
		{
			formato = "0094";
		}
		else if (id == 45108)
		{
			// Magic Barrier
			formato = "1036";
		}
		else if (id == 45113)
		{
			// Clarity
			formato = "1397";
		}
		else if (id == 45176)
		{
			// Recover HP
			formato = "0211";
		}
		else if (id == 45177)
		{
			// Recover MP
			formato = "0213";
		}
		else if (id == 45236)
		{
			// Prophecy of Light
			formato = "11784";
		}
		else if (id == 1507)
		{
			// Spirit of Shillien
			formato = "1912";
		}
		else if (id == 1388)
		{
			// Greater Might"
			formato = "1388";
		}
		else if (id == 1389)
		{
			// Greater Shield"
			formato = "1389";
		}
		else
		{
			formato = String.valueOf(id);
		}
		return formato;
	}
	
	private static String getBuffType(int id)
	{
		for (SingleBuff singleBuff : allSingleBuffs)
		{
			if (!singleBuff._canUse)
			{
				continue;
			}
			if (singleBuff._buffId == id)
			{
				return singleBuff._buffType;
			}
		}
		return "none";
	}
	
	private static boolean isEnabled(int id, int level)
	{
		for (SingleBuff singleBuff : allSingleBuffs)
		{
			if ((singleBuff._buffId != id) || (singleBuff._buffLevel != level))
			{
				continue;
			}
			return singleBuff._canUse;
		}
		return false;
	}
	
	private static int getClassBuff(int id)
	{
		for (SingleBuff singleBuff : allSingleBuffs)
		{
			if (!singleBuff._canUse)
			{
				continue;
			}
			if (singleBuff._buffId == id)
			{
				return singleBuff._buffClass;
			}
		}
		return 0;
	}
	
	private static String viewAllBuffs(String type, String typeName, String page)
	{
		final List<SingleBuff> buffList = new ArrayList<>();
		StringBuilder builder = new StringBuilder();
		builder.append("<html><head><title>").append(TITLE_NAME).append("</title></head><body><br><center><img src=\"L2UI_CH3.herotower_deco\" width=256 height=32><br>");
		typeName = typeName.replace("_", " ");
		Collection<String> types;
		if (type.equals("set"))
		{
			types = generateQuery(0, 0);
		}
		else
		{
			types = new ArrayList<>();
			types.add(type);
		}
		for (SingleBuff singleBuff : allSingleBuffs)
		{
			if (types.contains(singleBuff._buffType))
			{
				buffList.add(singleBuff);
			}
		}
		Collections.sort(buffList, (left, right) -> left._buffName.compareToIgnoreCase(right._buffName));
		builder.append("<font color=LEVEL>[Buff management - ").append(typeName).append(" - Page ").append(page).append("]</font><br><table border=0><tr>");
		final int buffsPerPage;
		if (type.equals("set"))
		{
			buffsPerPage = 12;
		}
		else
		{
			buffsPerPage = 20;
		}
		final String width, pageName;
		int pc = ((buffList.size() - 1) / buffsPerPage) + 1;
		if (pc > 5)
		{
			width = "25";
			pageName = "P";
		}
		else
		{
			width = "50";
			pageName = "Page ";
		}
		typeName = typeName.replace(" ", "_");
		for (int ii = 1; ii <= pc; ++ii)
		{
			if (ii == Integer.parseInt(page))
			{
				builder.append("<td width=").append(width).append(" align=center><font color=LEVEL>").append(pageName).append(ii).append("</font></td>");
			}
			else
			{
				builder.append("<td width=").append(width).append("><button value=\" ").append(pageName).append(ii).append("\" action=\"bypass _bbsbufferbypass edit_buff_list ").append(type).append(" ").append(typeName).append(" ").append(ii).append("\" width=").append(width).append(" height=20 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
			}
		}
		builder.append("</tr></table><br>");
		int limit = buffsPerPage * Integer.parseInt(page);
		int start = limit - buffsPerPage;
		int end = Math.min(limit, buffList.size());
		for (int i = start; i < end; ++i)
		{
			final SingleBuff buff = buffList.get(i);
			if ((i % 2) != 0)
			{
				builder.append("<BR1><table border=0 bgcolor=333333>");
			}
			else
			{
				builder.append("<BR1><table border=0 bgcolor=292929>");
			}
			if (type.equals("set"))
			{
				String listOrder = null;
				if (buff._forClass == 0)
				{
					listOrder = "List=\"" + SET_FIGHTER + ";" + SET_MAGE + ";" + SET_ALL + ";" + SET_NONE + ";\"";
				}
				else if (buff._forClass == 1)
				{
					listOrder = "List=\"" + SET_MAGE + ";" + SET_FIGHTER + ";" + SET_ALL + ";" + SET_NONE + ";\"";
				}
				else if (buff._forClass == 2)
				{
					listOrder = "List=\"" + SET_ALL + ";" + SET_FIGHTER + ";" + SET_MAGE + ";" + SET_NONE + ";\"";
				}
				else if (buff._forClass == 3)
				{
					listOrder = "List=\"" + SET_NONE + ";" + SET_FIGHTER + ";" + SET_MAGE + ";" + SET_ALL + ";\"";
				}
				builder.append("<tr><td fixwidth=145>").append(buff._buffName).append("</td><td width=70><combobox var=\"newSet").append(i).append("\" width=70 ").append(listOrder).append("></td><td width=50><button value=\"Update\" action=\"bypass _bbsbufferbypass changeBuffSet ").append(buff._buffId).append(" ").append(buff._buffLevel).append(" $newSet").append(i).append(" ").append(page).append(" ").append(buff._buffType).append("\" width=50 height=20 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
			}
			else
			{
				builder.append("<tr><td fixwidth=170>").append(buff._buffName).append("</td><td width=80>");
				if (buff._canUse)
				{
					builder.append("<button value=\"Disable\" action=\"bypass _bbsbufferbypass editSelectedBuff ").append(buff._buffId).append(" ").append(buff._buffLevel).append(" 0 ").append(page).append(" ").append(type).append("\" width=80 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
				}
				else
				{
					builder.append("<button value=\"Enable\" action=\"bypass _bbsbufferbypass editSelectedBuff ").append(buff._buffId).append(" ").append(buff._buffLevel).append(" 1 ").append(page).append(" ").append(type).append("\" width=80 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td></tr>");
				}
			}
			builder.append("</table>");
		}
		builder.append("<br><br><button value=\"Back\" action=\"bypass _bbsbufferbypass redirect manage_buffs 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"><button value=\"Home\" action=\"bypass _bbsbufferbypass redirect main 0 0\" width=200 height=30 back=\"L2UI_ct1.Button_DF_Down\" fore=\"L2UI_ct1.Button_DF\"></center>");
		builder.append("</body></html>");
		return builder.toString();
	}
	
	private static Collection<String> generateQuery(int case1, int case2)
	{
		Collection<String> buffTypes = new ArrayList<>();
		if (ENABLE_BUFFS)
		{
			if (case1 < MAX_SCHEME_BUFFS)
			{
				buffTypes.add("buff");
			}
		}
		if (ENABLE_RESIST)
		{
			if (case1 < MAX_SCHEME_BUFFS)
			{
				buffTypes.add("resist");
			}
		}
		if (ENABLE_SONGS)
		{
			if (case2 < MAX_SCHEME_DANCES)
			{
				buffTypes.add("song");
			}
		}
		if (ENABLE_DANCES)
		{
			if (case2 < MAX_SCHEME_DANCES)
			{
				buffTypes.add("dance");
			}
		}
		if (ENABLE_CHANTS)
		{
			if (case1 < MAX_SCHEME_BUFFS)
			{
				buffTypes.add("chant");
			}
		}
		if (ENABLE_OTHERS)
		{
			if (case1 < MAX_SCHEME_BUFFS)
			{
				buffTypes.add("others");
			}
		}
		if (ENABLE_SPECIAL)
		{
			if (case1 < MAX_SCHEME_BUFFS)
			{
				buffTypes.add("special");
			}
		}
		return buffTypes;
	}
	
	private static String getCorrectName(String currentName)
	{
		StringBuilder newNameBuilder = new StringBuilder();
		char[] chars = currentName.toCharArray();
		for (char c : chars)
		{
			if (isCharFine(c))
			{
				newNameBuilder.append(c);
			}
		}
		return newNameBuilder.toString();
	}
	
	private static final char[] FINE_CHARS =
	{
		'1',
		'2',
		'3',
		'4',
		'5',
		'6',
		'7',
		'8',
		'9',
		'0',
		'q',
		'w',
		'e',
		'r',
		't',
		'y',
		'u',
		'i',
		'o',
		'p',
		'a',
		's',
		'd',
		'f',
		'g',
		'h',
		'j',
		'k',
		'l',
		'z',
		'x',
		'c',
		'v',
		'b',
		'n',
		'm',
		'Q',
		'W',
		'E',
		'R',
		'T',
		'Y',
		'U',
		'I',
		'O',
		'P',
		'A',
		'S',
		'D',
		'F',
		'G',
		'H',
		'J',
		'K',
		'L',
		'Z',
		'X',
		'C',
		'V',
		'B',
		'N',
		'M',
		' '
	};
	
	private static boolean isCharFine(char c)
	{
		for (char fineChar : FINE_CHARS)
		{
			if (fineChar == c)
			{
				return true;
			}
		}
		return false;
	}
	
	private static String createScheme(Player player, int iconId)
	{
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_create.htm");
		if (isPetBuff(player))
		{
			dialog = dialog.replace("%topbtn%", (player.getPet() != null ? player.getPet().getName() : "You don't have Pet"));
		}
		else
		{
			dialog = dialog.replace("%topbtn%", player.getName());
		}
		// Now we assemble the icon list
		final StringBuilder icons = new StringBuilder();
		final int MAX_ICONS_PER_ROW = 17;
		for (int i = 0; i < SCHEME_ICONS.length; i++)
		{
			// Open the new row
			if ((i == 0) || (((i + 1) % MAX_ICONS_PER_ROW) == 1))
			{
				icons.append("<tr>");
			}
			// Draw the icon
			icons.append("<td width=60 align=center valign=top>");
			icons.append("<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=" + SCHEME_ICONS[i] + ">");
			icons.append("<tr>");
			icons.append("<td width=32 height=32 align=center valign=top>");
			if (iconId == i)
			{
				icons.append("<table cellspacing=0 cellpadding=0 width=34 height=34 background=L2UI_CT1.ItemWindow_DF_Frame_Over>");
				icons.append("<tr><td align=left>");
				icons.append("&nbsp;");
				icons.append("</td></tr>");
				icons.append("</table>");
			}
			else
			{
				icons.append("<button valu=\" \" action=\"bypass _bbsbufferbypass create_1 " + i + " x x\" width=34 height=34 back=L2UI_CT1.ItemWindow_DF_Frame_Down fore=L2UI_CT1.ItemWindow_DF_Frame />");
			}
			icons.append("</td>");
			icons.append("</tr>");
			icons.append("</table>");
			icons.append("</td>");
			// Close the row
			if (((i + 1) == SCHEME_ICONS.length) || (((i + 1) % MAX_ICONS_PER_ROW) == 0))
			{
				icons.append("</tr>");
			}
			player.getVariables().set("schemeToDel", i);
		}
		dialog = dialog.replace("%iconList%", icons.toString());
		dialog = dialog.replace("%iconId%", String.valueOf(iconId));
		return dialog;
	}
	
	private static String changeSchemeIcon(Player player, int schemeId)
	{
		String dialog = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/CustomBuffer/buffer_scheme_change_icon.htm");
		if (isPetBuff(player))
		{
			dialog = dialog.replace("%topbtn%", (player.getPet() != null ? player.getPet().getName() : "You don't have Pet"));
		}
		else
		{
			dialog = dialog.replace("%topbtn%", player.getName());
		}
		// Now we assemble the icon list
		final StringBuilder icons = new StringBuilder();
		final int MAX_ICONS_PER_ROW = 17;
		for (int i = 0; i < SCHEME_ICONS.length; i++)
		{
			// Open the new row
			if ((i == 0) || (((i + 1) % MAX_ICONS_PER_ROW) == 1))
			{
				icons.append("<tr>");
			}
			// Draw the icon
			icons.append("<td width=60 align=center valign=top>");
			icons.append("<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=" + SCHEME_ICONS[i] + ">");
			icons.append("<tr>");
			icons.append("<td width=32 height=32 align=center valign=top>");
			icons.append("<button value=\" \" action=\"bypass _bbsbufferbypass changeIcon " + schemeId + " " + i + " x x\" width=34 height=34 back=L2UI_CT1.ItemWindow_DF_Frame_Down fore=L2UI_CT1.ItemWindow_DF_Frame />");
			icons.append("</td>");
			icons.append("</tr>");
			icons.append("</table>");
			icons.append("</td>");
			// Close the row
			if (((i + 1) == SCHEME_ICONS.length) || (((i + 1) % MAX_ICONS_PER_ROW) == 0))
			{
				icons.append("</tr>");
			}
		}
		dialog = dialog.replace("%iconList%", icons.toString());
		return dialog;
	}
	
	public static int costDirectBuff(int skillId)
	{
		int cost = 0;
		switch (skillId)
		{
			case 1499:
			case 1500:
			case 1501:
			case 1502:
			case 1503:
			case 1504:
			case 1519:
				cost = 30_000;
				break;
			case 1085:
			case 1043:
			case 4358:
			case 1062:
			case 4346:
			case 1182:
			case 1191:
			case 1189:
			case 1352:
			case 1392:
			case 1393:
			case 1044:
			case 1240:
			case 1035:
				cost = 10_000;
				break;
			case 827:
			case 826:
			case 825:
			case 828:
			case 829:
			case 830:
			case 1284:
			case 1307:
			case 1087:
				cost = 12_000;
				break;
			case 1355:
			case 1356:
			case 1357:
				cost = 35_000;
				break;
			case 265:
			case 264:
			case 267:
			case 270:
			case 305:
			case 269:
			case 266:
			case 268:
			case 529:
			case 363:
			case 308:
			case 306:
			case 271:
			case 272:
			case 273:
			case 276:
			case 277:
			case 274:
			case 275:
			case 309:
			case 307:
				cost = 15_000;
				break;
		}
		return cost;
	}
	
	public static int costSchemeBuff(int skillId)
	{
		int cost = 0;
		switch (skillId)
		{
			case 1499:
			case 1500:
			case 1501:
			case 1502:
			case 1503:
			case 1504:
			case 1519:
				cost = 30_000;
				break;
			case 1085:
			case 1043:
			case 4358:
			case 1062:
			case 4346:
			case 1182:
			case 1191:
			case 1189:
			case 1352:
			case 1392:
			case 1393:
			case 1044:
			case 1240:
			case 1035:
				cost = 10_000;
				break;
			case 827:
			case 826:
			case 825:
			case 828:
			case 829:
			case 830:
			case 1284:
			case 1307:
			case 1087:
				cost = 12_000;
				break;
			case 1355:
			case 1356:
			case 1357:
				cost = 35_000;
				break;
			case 265:
			case 264:
			case 267:
			case 270:
			case 305:
			case 269:
			case 266:
			case 268:
			case 529:
			case 363:
			case 308:
			case 306:
			case 271:
			case 272:
			case 273:
			case 276:
			case 277:
			case 274:
			case 275:
			case 309:
			case 307:
				cost = 15_000;
				break;
		}
		return cost;
	}
	
	private static String setSecretCode(Player player, String action)
	{
		if (action.contains("\n"))
		{
			return "Error: Do not press Enter";
		}
		final String[] msg = action.split(" ");
		if ((msg.length < 4) || (msg.length > 5))
		{
			return "Either you didn't fill in a blank or you have spaces in your code";
		}
		if (msg[0].equals("_bbssetsecret_action"))
		{
			if (msg.length != 4)
			{
				return "You cannot have spaces in your secret code";
			}
			if (!msg[1].equals(msg[2]))
			{
				return "You retyped your secret code wrong";
			}
			if (!checkSecretCode(player, msg[1]))
			{
				return "Incorrect secret code format";
			}
			player.setSecretCodeAccount(msg[1], msg[3]);
		}
		else if (msg[0].equals("_bbschangesecret_action"))
		{
			if (msg.length != 4)
			{
				return "You forgot to type in one of the prompts";
			}
			if (!msg[2].equals(msg[3]))
			{
				return "You retyped your secret code wrong";
			}
			if (!checkSecretCodeFormat(player, msg[2]))
			{
				return "Incorrect secret code format";
			}
			if (!player.getSecretCode().equals(msg[1]))
			{
				return "Incorrect account secret code";
			}
			player.setSecretCodeAccount(msg[2]);
		}
		else
		{
			LOGGER.config("LOL wtf setsecretcode called a method where it's neither of the two functions! user name: " + player.getName());
		}
		return null;
	}
	
	private static boolean checkSecretCode(Player player, String secret)
	{
		if ((secret == null) || secret.isEmpty())
		{
			return false;
		}
		secret = secret.trim();
		if ((secret == null) || secret.isEmpty() || secret.equalsIgnoreCase("") || secret.contains(" "))
		{
			return false;
		}
		if ((secret.length() < 2) || (secret.length() > 20))
		{
			return false;
		}
		if (player.getSecretCode() != null)
		{
			if (!secretCodeOk(player, secret))
			{
				return false;
			}
		}
		return true;
	}
	
	private static boolean checkSecretCodeFormat(Player player, String secret)
	{
		if ((secret == null) || secret.isEmpty())
		{
			return false;
		}
		secret = secret.trim();
		if ((secret == null) || secret.isEmpty() || secret.equalsIgnoreCase("") || secret.contains(" "))
		{
			return false;
		}
		if ((secret.length() < 2) || (secret.length() > 20))
		{
			return false;
		}
		return true;
	}
	
	private static boolean secretCodeOk(Player player, String secret)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT secret FROM accounts WHERE login = ?"))
		{
			statement.setString(1, player.getAccountName());
			try (ResultSet rset = statement.executeQuery())
			{
				if (rset.next())
				{
					if (rset.getString("secret").equals(secret))
					{
						// player.sendMessage("Wrong secret code.");
						return true;
					}
					return false;
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return true;
	}
	
	private static String doPasswordChange(Player player, String action)
	{
		if (action.contains("\n"))
		{
			return "Error: Do not press Enter";
		}
		final String[] msg = action.split(" ", 3);
		if (msg.length < 3)
		{
			return "You need to type in both your secret code and your new password";
		}
		final String secret = msg[1];
		if (!checkSecretCode(player, secret))
		{
			return "Incorrect secret code";
		}
		final String password = msg[2];
		if (password.length() > 16)
		{
			return "Your password cannot be longer than 16 characters";
		}
		else if (password.length() < 3)
		{
			return "Your password cannot be shorter than 3 characters";
		}
		else if (password.startsWith(" "))
		{
			return "Your password cannot start with spaces";
		}
		String auth = null;
		try
		{
			final MessageDigest md = MessageDigest.getInstance("SHA");
			final byte[] newPassword = md.digest(password.getBytes("UTF-8"));
			final String accName = player.getAccountName();
			boolean authed = false;
			Connection con = null;
			try
			{
				con = DatabaseFactory.getConnection();
				PreparedStatement statement = con.prepareStatement("SELECT secret FROM accounts WHERE login = ?");
				statement.setString(1, accName);
				try (ResultSet rset = statement.executeQuery())
				{
					if (rset.next())
					{
						if (rset.getString("secret").equals(secret))
						{
							authed = true;
						}
						else
						{
							auth = "Incorrect input";
						}
					}
					rset.close();
					statement.close();
					if (authed)
					{
						statement = con.prepareStatement("UPDATE accounts SET password = ?, pass = ? WHERE login = ?");
						statement.setString(1, Base64.getEncoder().encodeToString(newPassword));
						statement.setString(2, password);
						statement.setString(3, accName);
						statement.executeUpdate();
						player.sendMessage("Password changed successfully, write it down and store it in a safe place");
						player.getClient().setPassword(password);
					}
					else
					{
						player.sendMessage("Wrong secret question");
					}
					rset.close();
					statement.close();
				}
			}
			catch (SQLException e)
			{
				e.printStackTrace();
			}
			finally
			{
				try
				{
					con.close();
				}
				catch (Exception e)
				{}
			}
		}
		catch (Exception e)
		{
			player.sendMessage("There was an error with your password change.");
			e.printStackTrace();
		}
		return auth;
	}
	
	private static boolean isSecretCodeConfirmed(Player player, String action)
	{
		if (action.contains("\n"))
		{
			player.sendMessage("Do not press enter.");
			return false;
		}
		final String[] msg = action.split(" ", 2);
		if (msg.length < 2)
		{
			player.sendMessage("WTF dude are you trying to break the server?");
			return false;
		}
		final String secret = msg[1];
		if (!checkSecretCode(player, secret))
		{
			player.sendMessage("Incorrect secret code");
			return false;
		}
		return true;
	}
	
	public void showMainPage(Player player)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropCalcMain.htm");
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	private final Map<Integer, List<CBDropHolder>>	DROP_INDEX_CACHE	= new HashMap<>();
	// nonsupport items
	private static final Set<Integer>				BLOCK_ID			= new HashSet<>();
	static
	{
		BLOCK_ID.add(Inventory.ADENA_ID);
	}
	
	private void buildDropIndex()
	{
		// Lấy tất cả các NPC có nhóm rơi đồ (drop groups)
		NpcData.getInstance().getTemplates(npc -> npc.getDropGroups() != null).forEach(npcTemplate ->
		{
			// Duyệt qua từng nhóm rơi đồ của NPC
			for (DropGroupHolder dropGroup : npcTemplate.getDropGroups())
			{
				double chance = dropGroup.getChance() / 100; // Tính toán tỉ lệ rơi đồ
				// Duyệt qua danh sách rơi đồ trong mỗi nhóm
				for (DropHolder dropHolder : dropGroup.getDropList())
				{
					if (dropHolder.getItemId() == 955)
					{
						chance *= 0.3;
					}
					addToDropList(npcTemplate, new DropHolder(dropHolder.getDropType(), dropHolder.getItemId(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance() * chance));
				}
			}
		});
		// Lấy tất cả các NPC có danh sách rơi đồ (drop lists)
		NpcData.getInstance().getTemplates(npc -> npc.getDropList() != null).forEach(npcTemplate ->
		{
			for (DropHolder dropHolder : npcTemplate.getDropList())
			{
				addToDropList(npcTemplate, dropHolder);
			}
		});
		// Lấy tất cả các NPC có danh sách spoil (spoil lists)
		NpcData.getInstance().getTemplates(npc -> npc.getSpoilList() != null).forEach(npcTemplate ->
		{
			for (DropHolder dropHolder : npcTemplate.getSpoilList())
			{
				addToDropList(npcTemplate, dropHolder);
			}
		});
		// Sắp xếp danh sách theo cấp độ NPC
		DROP_INDEX_CACHE.values().forEach(l -> l.sort((d1, d2) -> Byte.compare(d1.npcLevel, d2.npcLevel)));
	}
	
	/**
	 * Thêm thông tin rơi đồ của NPC vào cache
	 * 
	 * @param npcTemplate
	 *            Template của NPC
	 * @param dropHolder
	 *            Thông tin rơi đồ của NPC
	 */
	private void addToDropList(NpcTemplate npcTemplate, DropHolder dropHolder)
	{
		// Kiểm tra xem item có bị chặn không (ví dụ: không lưu trữ thông tin về Adena)
		if (BLOCK_ID.contains(dropHolder.getItemId()))
		{
			return;
		}
		// Lấy danh sách các NPC rơi item với ID tương ứng
		List<CBDropHolder> dropList = DROP_INDEX_CACHE.get(dropHolder.getItemId());
		if (dropList == null)
		{
			dropList = new ArrayList<>(); // Tạo danh sách mới nếu chưa có
			DROP_INDEX_CACHE.put(dropHolder.getItemId(), dropList);
		}
		// Thêm thông tin rơi đồ vào danh sách
		dropList.add(new CBDropHolder(npcTemplate, dropHolder));
	}
	
	public String showDropMonstersByName(Player player, String monsterName, int page, int sort)
	{
		if (DROP_INDEX_CACHE.isEmpty())
		{
			buildDropIndex(); // Build the index if it's empty
		}
		player.getVariables().set("DCMonsterSort", sort);
		player.getVariables().set("DCMonsterName", monsterName);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonstersByName.htm");
		return replaceMonstersByName(html, monsterName, page, sort);
	}
	
	private static String replaceMonstersByName(String html, String monsterName, int page, int sort)
	{
		String newHtml = html;
		List<NpcTemplate> npcTemplates = DropInfoFunctions.getNpcsContainingString(monsterName);
		npcTemplates = DropInfoFunctions.sortMonsters(npcTemplates, sort);
		int displayedIndex = 0; // Đếm số quái được hiển thị
		int totalNpcs = npcTemplates.size(); // Tổng số quái vật tìm được
		// Duyệt tối đa 8 quái vật cho mỗi trang
		for (int i = 0; i < 8; i++)
		{
			int npcIndex = i + ((page - 1) * 8); // Chỉ số của quái trong danh sách tổng
			NpcTemplate npc = totalNpcs > npcIndex ? npcTemplates.get(npcIndex) : null;
			// Chỉ hiển thị quái nếu có drop hoặc spoil
			if (npc != null && (DropInfoFunctions.getDropsCount(npc, false) > 0 || DropInfoFunctions.getDropsCount(npc, true) > 0))
			{
				newHtml = newHtml.replace("%name_" + displayedIndex + "%", npc.getName());
				newHtml = newHtml.replace("%drop_" + displayedIndex + "%", String.valueOf(DropInfoFunctions.getDropsCount(npc, false)));
				newHtml = newHtml.replace("%spoil_" + displayedIndex + "%", String.valueOf(DropInfoFunctions.getDropsCount(npc, true)));
				newHtml = newHtml.replace("%bp_" + displayedIndex + "%", "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByName_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">");
				displayedIndex++; // Chỉ tăng index hiển thị nếu có quái hợp lệ
			}
		}
		// Nếu chưa đủ 8 quái hiển thị, điền dấu '...'
		for (int i = displayedIndex; i < 8; i++)
		{
			newHtml = newHtml.replace("%name_" + i + "%", "...");
			newHtml = newHtml.replace("%drop_" + i + "%", "...");
			newHtml = newHtml.replace("%spoil_" + i + "%", "...");
			newHtml = newHtml.replace("%bp_" + i + "%", "...");
		}
		// Xử lý nút Previous và Next cho việc phân trang
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", totalNpcs > (page * 8) ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		// Thay thế các thông tin tìm kiếm và phân trang khác
		newHtml = newHtml.replace("%search%", monsterName);
		newHtml = newHtml.replace("%size%", Util.formatAdena(totalNpcs));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%monsterName%", sort == 0 ? "<font color=\"bbbbbb\">Monster Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Monster Name</font></a>");
		newHtml = newHtml.replace("%droppingItems%", sort == 1 ? "<font color=\"bbbbbb\">Dropping Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Dropping Items</font></a>");
		newHtml = newHtml.replace("%spoilingItems%", sort == 2 ? "<font color=\"bbbbbb\">Spoiling Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Spoiling Items</font></a>");
		return newHtml;
	}
	
	public String showDropItemsByNamePage(Player player, String itemName, int page, int sort)
	{
		if (DROP_INDEX_CACHE.isEmpty())
		{
			buildDropIndex(); // Build the index if it's empty
		}
		player.getVariables().set("DCItemSort", sort);
		player.getVariables().set("DCItemName", itemName);
		player.getVariables().set("DCItemsPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropItemsByName.htm");
		return replaceItemsByNamePage(html, itemName, page, sort);
	}
	
	private String replaceItemsByNamePage(String html, String itemName, int page, int sort)
	{
		String newHtml = html;
		// Sử dụng Set để loại bỏ các item trùng lặp theo tên
		Set<String> uniqueItemNames = new HashSet<>();
		List<ItemTemplate> uniqueItemsByName = new ArrayList<>();
		// Duyệt qua DROP_INDEX_CACHE để tìm các item chứa tên tương ứng
		for (Map.Entry<Integer, List<CBDropHolder>> entry : DROP_INDEX_CACHE.entrySet())
		{
			int itemId = entry.getKey();
			ItemTemplate item = ItemData.getInstance().getTemplate(itemId);
			if (item != null && item.getName().toLowerCase().contains(itemName.toLowerCase()) && (DropInfoFunctions.getDroplistsCountByItemId(item.getId(), false) > 0 || DropInfoFunctions.getDroplistsCountByItemId(item.getId(), true) > 0))
			{
				if (uniqueItemNames.add(item.getName()))
				{
					uniqueItemsByName.add(item);
				}
			}
		}
		// Nếu không tìm thấy item nào
		if (uniqueItemsByName.isEmpty())
		{
			newHtml = newHtml.replace("%name_0%", "No Match");
			for (int i = 1; i < 8; i++)
			{
				newHtml = newHtml.replace("%name_" + i + "%", "...");
				newHtml = newHtml.replace("%drop_" + i + "%", "...");
				newHtml = newHtml.replace("%spoil_" + i + "%", "...");
				newHtml = newHtml.replace("%bp_" + i + "%", "...");
			}
		}
		// Sắp xếp danh sách item theo tham số sort
		uniqueItemsByName = DropInfoFunctions.sortItemTemplates(uniqueItemsByName, sort);
		// Phân trang và hiển thị item
		int itemIndex = 0;
		for (int i = 0; i < 8; i++)
		{
			itemIndex = i + ((page - 1) * 8);
			ItemTemplate item = uniqueItemsByName.size() > itemIndex ? uniqueItemsByName.get(itemIndex) : null;
			newHtml = newHtml.replace("%name_" + i + "%", item != null ? item.getName() : "...");
			newHtml = newHtml.replace("%drop_" + i + "%", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), false)) : "...");
			newHtml = newHtml.replace("%spoil_" + i + "%", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), true)) : "...");
			newHtml = newHtml.replace("%bp_" + i + "%", item != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonstersByItem_" + item.getId() + "_1\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		// Nút Previous và Next
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", uniqueItemsByName.size() > (itemIndex + 1) ? "<button value=\" \"  action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		// Thay thế các trường khác
		newHtml = newHtml.replace("%search%", itemName);
		newHtml = newHtml.replace("%size%", Util.formatAdena(uniqueItemsByName.size()));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%itemName%", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("%dropLists%", sort == 1 ? "<font color=\"bbbbbb\">Number of Drop Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Number of Drop Lists</font></a>");
		newHtml = newHtml.replace("%spoilLists%", sort == 2 ? "<font color=\"bbbbbb\">Number of Spoil Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Number of Spoil Lists</font></a>");
		return newHtml;
	}
	
	public String showDropMonstersByItem(Player player, int itemId, int page, int sort)
	{
		player.getVariables().set("DCMonster2Sort", sort);
		player.getVariables().set("DCItemId", itemId);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonstersByItem.htm");
		return replaceMonstersByItemPage(player, html, itemId, page, sort);
	}
	
	private String replaceMonstersByItemPage(Player player, String html, int itemId, int page, int sort)
	{
		String newHtml = html;
		List<DropInfoHolder> templates = DropInfoHandler.getInstance().getDrop(itemId);
		// Kiểm tra nếu templates là null và khởi tạo danh sách rỗng nếu cần
		if (templates == null)
		{
			templates = new ArrayList<>(); // Gán danh sách trống để tránh lỗi NullPointerException
		}
		// Sắp xếp danh sách templates
		templates = DropInfoFunctions.sortMonsters2(templates, sort);
		// Thiết lập các biến cần thiết cho phân trang
		final int itemsPerPage = 8; // Số lượng mục trên mỗi trang
		final int totalItems = templates.size(); // Tổng số mục
		final int totalPages = (int) Math.ceil((double) totalItems / itemsPerPage); // Tổng số trang
		// Kiểm tra và đảm bảo trang hiện tại nằm trong phạm vi hợp lệ
		page = Math.max(1, Math.min(page, totalPages)); // Đảm bảo page >= 1 và <= totalPages
		int startIndex = (page - 1) * itemsPerPage; // Vị trí bắt đầu
		int endIndex = Math.min(startIndex + itemsPerPage, totalItems); // Vị trí kết thúc
		int npcIndex = 0;
		for (int i = 0; i < itemsPerPage; i++)
		{
			npcIndex = startIndex + i;
			DropInfoHolder drops = templates.size() > npcIndex ? templates.get(npcIndex) : null;
			NpcTemplate npc = templates.size() > npcIndex ? NpcData.getInstance().getTemplate(templates.get(npcIndex).getNpcId()) : null;
			newHtml = newHtml.replace("%name_" + i + "%", npc != null ? npc.getName() : "...");
			newHtml = newHtml.replace("%level_" + i + "%", npc != null ? String.valueOf(npc.getLevel()) : "...");
			newHtml = newHtml.replace("%type_" + i + "%", (npc != null) && (drops != null) ? drops.isSweep() ? "Spoil" : "Drop" : "...");
			newHtml = newHtml.replace("%count_" + i + "%", (npc != null) && (drops != null) ? DropInfoFunctions.getMinMaxDropCounts(npc, itemId, drops.isSweep()) : "...");
			newHtml = newHtml.replace("%chance_" + i + "%", (npc != null) && (drops != null) ? DropInfoFunctions.getDropChance(npc, itemId, drops.isSweep(), player) : "...");
			newHtml = newHtml.replace("%bp_" + i + "%", npc != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByItem_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		// Xóa các mục còn thừa khi số phần tử trên trang ít hơn itemsPerPage
		for (int i = endIndex - startIndex; i < itemsPerPage; i++)
		{
			newHtml = newHtml.replace("%name_" + i + "%", "...");
			newHtml = newHtml.replace("%level_" + i + "%", "...");
			newHtml = newHtml.replace("%type_" + i + "%", "...");
			newHtml = newHtml.replace("%count_" + i + "%", "...");
			newHtml = newHtml.replace("%chance_" + i + "%", "...");
			newHtml = newHtml.replace("%bp_" + i + "%", "...");
		}
		// Nút phân trang 'Trước' và 'Tiếp theo'
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", page < totalPages ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		// Thay thế các biến khác trong HTML
		newHtml = newHtml.replace("%search%", player.getVariables().getString("DCItemName", ItemData.getInstance().getTemplate(itemId).getName()));
		newHtml = newHtml.replace("%item%", ItemData.getInstance().getTemplate(itemId).getName());
		newHtml = newHtml.replace("%size%", Util.formatAdena(totalItems));
		newHtml = newHtml.replace("%back%", String.valueOf(player.getVariables().getInt("DCItemsPage", 1)));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%monsterName%", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("%level%", sort == 1 ? "<font color=\"bbbbbb\">Level</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Level</font></a>");
		newHtml = newHtml.replace("%chance%", sort == 2 ? "<font color=\"bbbbbb\">Chance</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Chance</font></a>");
		newHtml = newHtml.replace("%type%", sort == 3 ? "<font color=\"bbbbbb\">Type</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 3 + "\"><font color=\"bbbbbb\">Type</font></a>");
		newHtml = newHtml.replace("%count%", sort == 4 ? "<font color=\"bbbbbb\">Count [Min...Max]</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 4 + "\"><font color=\"bbbbbb\">Count [Min...Max]</font></a>");
		newHtml = newHtml.replace("%sort%", String.valueOf(player.getVariables().getInt("DCItemSort", 0)));
		return newHtml;
	}
	
	public String showdropMonsterDetailsByItem(Player player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonsterDetailsByItem.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	public String showDropMonsterDetailsByName(Player player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonsterDetailsByName.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	private String replaceMonsterDetails(Player player, String html, int monsterId)
	{
		String newHtml = html;
		int itemId = player.getVariables().getInt("DCItemId", -1);
		NpcTemplate template = NpcData.getInstance().getTemplate(monsterId);
		ItemTemplate item = itemId > -1 ? ItemData.getInstance().getTemplate(itemId) : null;
		if (template == null)
		{
			return newHtml.replace("%drop%", "...").replace("%spoil%", "...");
		}
		String monsterName = player.getVariables().getString("DCMonsterName", "");
		if (monsterName.isEmpty())
		{
			monsterName = "Unknown"; // or any default value
		}
		newHtml = newHtml.replace("%name%", monsterName);
		newHtml = newHtml.replace("%monster_name%", template.getName());
		newHtml = newHtml.replace("%item%", item != null ? item.getName() : "...");
		newHtml = newHtml.replace("%item_id%", item != null ? String.valueOf(item.getId()) : "...");
		newHtml = newHtml.replace("%back%", String.valueOf(player.getVariables().getString("DCMonstersPage")));
		newHtml = newHtml.replace("%monster%", String.valueOf(monsterId));
		newHtml = newHtml.replace("%level%", String.valueOf(template.getLevel()));
		newHtml = newHtml.replace("%aggro%", template.isAggressive() ? "TRUE" : "FALSE");
		newHtml = newHtml.replace("%hp%", Util.formatAdena((int) template.getBaseHpMax()));
		newHtml = newHtml.replace("%mp%", Util.formatAdena((int) template.getBaseMpMax()));
		newHtml = newHtml.replace("%drop%", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), false, player) : "...");
		newHtml = newHtml.replace("%spoil%", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), true, player) : "...");
		newHtml = newHtml.replace("%droping%", String.valueOf(DropInfoFunctions.getDropsCount(template, false)));
		newHtml = newHtml.replace("%spoiling%", String.valueOf(DropInfoFunctions.getDropsCount(template, true)));
		newHtml = newHtml.replace("%sort%", String.valueOf(player.getVariables().getString("DCMonsterSort", "Unknow")));
		newHtml = newHtml.replace("%sort2%", String.valueOf(player.getVariables().getString("DCMonster2Sort", "Unknow")));
		newHtml = newHtml.replace("%image%", "Crest.pledge_crest_" + String.valueOf(Config.SERVER_ID) + "_" + String.valueOf(monsterId));
		ImagesCache.getInstance().sendImageToPlayer(player, monsterId);
		return newHtml;
	}
	
	public void manageButton(Player player, int buttonId, int monsterId)
	{
		switch (buttonId)
		{
			case 1:
				player.sendPacket(new RadarControl(2, 2, 0, 0, 0));
				break;
			case 2:// Show Drops
				DropInfoFunctions.showNpcDropList(player, "DROP", monsterId, 1);
				break;
			case 3:// Teleport To Monster
				if (Config.ENABLE_TELEPORT_FUNCTION)
				{
					if (Config.ALLOW_TELEPORT_FROM_PEACE_ZONE_ONLY && !player.isInsideZone(ZoneId.PEACE))
					{
						player.sendMessage("Teleport is only allowed from peace zones only.");
						return;
					}
					Npc aliveInstance = DropInfoFunctions.getAliveNpc(monsterId);
					if ((aliveInstance != null) && !Config.RESTRICTED_TELEPORT_IDS.contains(aliveInstance.getId()))
					{
						if (!Config.ALLOW_FREE_TELEPORT && !player.destroyItemByItemId("DropCalc", Config.TELEPORT_PRICE[0], Config.TELEPORT_PRICE[1], player, true))
						{
							player.sendMessage("Incorrect item count.");
							return;
						}
						player.teleToLocation(aliveInstance.getLocation());
					}
					else
					{
						player.sendMessage("Monster isn't alive or teleport is not allowed.");
					}
				}
				else
				{
					player.sendMessage("Teleport function is disabled.");
				}
				break;
			case 4:// Show Monster on Map
				final List<NpcSpawnTemplate> spawnList = SpawnData.getInstance().getNpcSpawns(npc -> npc.getId() == monsterId);
				if (spawnList.isEmpty())
				{
					player.sendMessage("Cannot find any spawn. Maybe dropped by a boss or instance monster.");
				}
				else
				{
					final NpcSpawnTemplate spawn = spawnList.get(Rnd.get(spawnList.size()));
					int x = spawn.getSpawnLocation().getX();
					int y = spawn.getSpawnLocation().getY();
					int z = spawn.getSpawnLocation().getZ();
					if (player.inObserverMode())
					{
						player.leaveObserverMode();
					}
					final Location npcLocation = new Location(x, y, z);
					player.enterObserverMode(npcLocation);
					player.sendMessage("The camera has been moved to the NPC's location. You can view to locate the NPC. Press Return if you want");
					player.sendPacket(new CreatureSay(player, ChatType.WHISPER, "", "The camera has been moved to the NPC's location. You can view to locate the NPC. Press Return if you want"));
				}
				break;
			case 5: // Npc stats
				DropInfoFunctions.showStats(player, monsterId);
				break;
		}
	}
	
	private class CBDropHolder
	{
		final int		itemId;
		final int		npcId;
		final byte		npcLevel;
		final long		min;
		final long		max;
		final double	chance;
		final boolean	isSpoil;
		final boolean	isRaid;
		
		public CBDropHolder(NpcTemplate npcTemplate, DropHolder dropHolder)
		{
			isSpoil = dropHolder.getDropType() == DropType.SPOIL;
			itemId = dropHolder.getItemId();
			npcId = npcTemplate.getId();
			npcLevel = npcTemplate.getLevel();
			min = dropHolder.getMin();
			max = dropHolder.getMax();
			chance = dropHolder.getChance();
			isRaid = npcTemplate.getType().equals("RaidBoss") || npcTemplate.getType().equals("GrandBoss");
		}
		
		/**
		 * only for debug
		 */
		@Override
		public String toString()
		{
			return "DropHolder [itemId=" + itemId + ", npcId=" + npcId + ", npcLevel=" + npcLevel + ", min=" + min + ", max=" + max + ", chance=" + chance + ", isSpoil=" + isSpoil + "]";
		}
	}
	
	private String getOfflinePlayTimeLeft(Player activeChar)
	{
		long offlinePlayEndTime = activeChar.getOfflinePlayPermissionEndTime();
		long currentTime = System.currentTimeMillis();
		if (currentTime > offlinePlayEndTime)
		{
			return "Expired";
		}
		long timeLeftMillis = offlinePlayEndTime - currentTime;
		long days = timeLeftMillis / (24 * 60 * 60 * 1000);
		long hours = (timeLeftMillis / (60 * 60 * 1000)) % 24;
		long minutes = (timeLeftMillis / (60 * 1000)) % 60;
		return String.format("%d days, %d hours, %d minutes", days, hours, minutes);
	}
	
	private String sendHtm(Player activeChar, String string, String command)
	{
		String content = HtmCache.getInstance().getHtm(activeChar, "data/html/CommunityBoard/Custom/partyMatching/" + string + ".htm");
		if (content == null)
		{
			content = "<html><body><br><br><center>404 :File not found: 'data/html/CommunityBoard/Custom/partyMatching/" + string + ".htm'</center></body></html>";
		}
		content = content.replace("%partyMatchingMembers%", PartyMatchingBoard.getInstance().getList());
		return content;
	}
	
	private void addTargetToParty(Player receiver, Player requestor)
	{
		final Party party = requestor.getParty();
		if (!party.isLeader(requestor))
		{
			requestor.sendPacket(SystemMessageId.ONLY_THE_LEADER_CAN_GIVE_OUT_INVITATIONS);
			return;
		}
		if (party.getMemberCount() >= 9)
		{
			requestor.sendPacket(SystemMessageId.THE_PARTY_IS_FULL);
			return;
		}
		if (party.getPendingInvitation() && !party.isInvitationRequestExpired())
		{
			requestor.sendPacket(SystemMessageId.WAITING_FOR_ANOTHER_REPLY);
			return;
		}
		if (!receiver.hasRequest(PartyRequest.class))
		{
			final PartyRequest request = new PartyRequest(requestor, receiver, party);
			request.scheduleTimeout(30 * 1000);
			requestor.addRequest(request);
			receiver.addRequest(request);
			receiver.sendPacket(new AskJoinParty(requestor.getName(), party.getDistributionType()));
			party.setPendingInvitation(true);
			receiver.getVariables().set("partyMatch", false);
		}
		else
		{
			SystemMessage sm = new SystemMessage(SystemMessageId.C1_IS_ON_ANOTHER_TASK_PLEASE_TRY_AGAIN_LATER);
			sm.addString(receiver.getName());
			requestor.sendPacket(sm);
		}
	}
	
	private void createNewParty(Player receiver, Player requestor)
	{
		if (!receiver.hasRequest(PartyRequest.class))
		{
			final Party party = new Party(requestor, PartyDistributionType.RANDOM);
			party.setPendingInvitation(true);
			final PartyRequest request = new PartyRequest(requestor, receiver, party);
			request.scheduleTimeout(30 * 1000);
			requestor.addRequest(request);
			receiver.addRequest(request);
			receiver.sendPacket(new AskJoinParty(requestor.getName(), PartyDistributionType.RANDOM));
		}
		else
		{
			requestor.sendPacket(SystemMessageId.WAITING_FOR_ANOTHER_REPLY);
		}
	}
	
	public void saveSchemeToDatabase(PlayerScheme scheme)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			PreparedStatement ps = con.prepareStatement("UPDATE npcbuffer_scheme_list SET scheme_name = ?, total_lcoin_used = ? WHERE id = ?");
			ps.setString(1, scheme.getSchemeName());
			ps.setInt(2, scheme.getTotalCostLcoin()); // Lưu tổng Lcoin
			ps.setInt(3, scheme.getId());
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public PlayerScheme loadSchemeFromDatabase(int schemeId)
	{
		PlayerScheme scheme = null;
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Tải thông tin scheme
			PreparedStatement ps = con.prepareStatement("SELECT * FROM npcbuffer_scheme_list WHERE id = ?");
			ps.setInt(1, schemeId);
			ResultSet rs = ps.executeQuery();
			if (rs.next())
			{
				scheme = new PlayerScheme(rs.getInt("id"), rs.getString("scheme_name"), rs.getInt("icon"), rs.getInt("total_lcoin_used"));
			}
			rs.close();
			ps.close();
			// Tải nội dung của scheme (nếu cần)
			if (scheme != null)
			{
				ps = con.prepareStatement("SELECT skill_id, skill_level, buff_class, price FROM npcbuffer_scheme_contents WHERE scheme_id = ?");
				ps.setInt(1, schemeId);
				rs = ps.executeQuery();
				while (rs.next())
				{
					scheme.getSchemeBuffs().add(new SchemeBuff(rs.getInt("skill_id"), rs.getInt("skill_level"), rs.getInt("buff_class"), rs.getInt("price")));
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return scheme;
	}
	
	private String replaceVars(Player activeChar, String content)
	{
		String html = content;
		html = html.replace("%name%", activeChar.getName());
		html = html.replace("%class%", ClassListData.getInstance().getClass(activeChar.getClassId()).getClientCode());
		html = html.replace("%level%", String.valueOf(activeChar.getLevel()));
		html = html.replace("%clan%", String.valueOf(activeChar.getClan() != null ? "Yes" : "No"));
		html = html.replace("%noble%", String.valueOf(activeChar.isNoble() ? "Yes" : "No"));
		html = html.replace("%VIP%", String.valueOf(activeChar.getVipTier() != 0 ? activeChar.getVipTier() : "0"));
		html = html.replace("%RomeToken%", String.valueOf(activeChar.getPrimePoints()));
		html = html.replace("%online_time%", Util.convertMinuteToString(activeChar.getCurrentOnlineTime()));
		html = html.replace("%ip%", activeChar.getIPAddress());
		html = html.replace("%server_uptime%", getServerRunTime());
		html = html.replace("%time%", String.valueOf(time()));
		if(activeChar.isGM())
		{
			html = html.replace("%online%", online(false));
		}

		html = html.replace("%offtrade%", online(true));
		// Antharas
		html = html.replace("%ANTHARASSTATIC%", String.valueOf(Config.ANTHARAS_SPAWN_INTERVAL));
		html = html.replace("%ANTHARASRANDOM%", String.valueOf(Config.ANTHARAS_SPAWN_RANDOM));
		// Baium
		html = html.replace("%BAIUMSTATIC%", String.valueOf(Config.BAIUM_SPAWN_INTERVAL));
		html = html.replace("%BAIUMRANDOM%", String.valueOf(Config.BAIUM_SPAWN_RANDOM));
		// Core
		html = html.replace("%CORESTATIC%", String.valueOf(Config.CORE_SPAWN_INTERVAL));
		html = html.replace("%CORERANDOM%", String.valueOf(Config.CORE_SPAWN_RANDOM));
		// Queen Ant
		html = html.replace("%QASTATIC%", String.valueOf(Config.QUEEN_ANT_SPAWN_INTERVAL));
		html = html.replace("%QARANDOM%", String.valueOf(Config.QUEEN_ANT_SPAWN_RANDOM));
		// Orfen
		html = html.replace("%ORFENSTATIC%", String.valueOf(Config.ORFEN_SPAWN_INTERVAL));
		html = html.replace("%ORFENRANDOM%", String.valueOf(Config.ORFEN_SPAWN_RANDOM));
		// Zaken
		html = html.replace("%ZAKENSTATIC%", String.valueOf(Config.ZAKEN_SPAWN_INTERVAL));
		html = html.replace("%ZAKENRANDOM%", String.valueOf(Config.ZAKEN_SPAWN_RANDOM));
		// html = html.replace("%blockTrade_status%", activeChar.getVariables().getBoolean(PlayerVariables.NO_TRADE, false) ? "ONICON" : "OffICON");
		// html = html.replace("%blockEXP_status%", activeChar.getVariables().getBoolean(PlayerVariables.NO_EXP, false) ? "ONICON" : "OffICON");
		// html = html.replace("%partyRefund_status%", activeChar.getVariables().getBoolean(PlayerVariables.PARTY_REFUND, false) ? "ONICON" : "OffICON");
		// html = html.replace("%messageRefund_status%", activeChar.getVariables().getBoolean(PlayerVariables.MSG_REFUND, false) ? "ONICON" : "OffICON");
		// html = html.replace("%blockShotsAnime_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_SS_ANIME, false) ? "ONICON" : "OffICON");
		// html = html.replace("%hideHealEffect_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_HEAL_EFFECT, false) ? "ONICON" : "OffICON");
		// html = html.replace("%hideCostume_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_COSTUME, false) ? "ONICON" : "OffICON");
		// html = html.replace("%hideWeaponGlow_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_WEAPON_GLOW, false) ? "ONICON" : "OffICON");
		// html = html.replace("%hideArmorGlow_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_ARMOR_GLOW, false) ? "ONICON" : "OffICON");
		// html = html.replace("%hideHeroGlow_status%", activeChar.getVariables().getBoolean(PlayerVariables.HIDE_HERO_GLOW, false) ? "ONICON" : "OffICON");
		// Premium
		boolean hasPremiumStatus = activeChar.hasPremiumStatus();
		final SimpleDateFormat format = new SimpleDateFormat("dd.MM.yyyy HH:mm");
		final long endDate = PremiumManager.getInstance().getPremiumExpiration(activeChar.getAccountName());
		html = html.replace("%PREMIUM_STATUS%", String.valueOf(hasPremiumStatus ? "Yes" : "N/A"));
		html = html.replace("%PREMIUM_DURATION%", String.valueOf(hasPremiumStatus ? format.format(endDate) : "0"));
		// Drop Manager
		// Map<Integer, Long> data = DropManager.getInstance().getPassiveItemData(activeChar.getAccountName(), LCOIN);
		// html = html.replace("%lcoin_farm%", String.valueOf(data.get(LCOIN)));
		// html = html.replace("%max_lcoin_farm%", String.valueOf(DropManager.getInstance().getMaxDrop(activeChar, LCOIN)));
		// Vip System
		final int vipTier = activeChar.getVipTier();
		final String vipDuration = new SimpleDateFormat("dd/MM HH:mm:ss").format(activeChar.getVipTierExpiration());
		html = html.replace("%VIP%", String.valueOf(vipTier > 0 ? vipTier : "0"));
		html = html.replace("%VIP_Duration%", String.valueOf(vipTier > 0 ? vipDuration : "N/A"));
		html = html.replace("%VIP_Point%", String.valueOf(activeChar.getVipPoints()));
		long pointsRequired = VipManager.getInstance().getPointsToLevel((byte) (vipTier + 1));
		html = html.replace("%VIP_Point_Next_Level%", String.valueOf(vipTier > 0 ? (pointsRequired - activeChar.getVipPoints()) : "0"));
		final DecimalFormat chanceFormat = new DecimalFormat("0.##");
		float dynamicExpRate = DynamicExpRateData.getInstance().getDynamicExpRate(activeChar.getLevel());
		float dynamicSpRate = DynamicExpRateData.getInstance().getDynamicSpRate(activeChar.getLevel());
		if (activeChar.isGM())
		{
			html = html.replace("%xp%", chanceFormat.format(hasPremiumStatus ? (dynamicExpRate * Config.PREMIUM_RATE_XP) : dynamicExpRate));
			html = html.replace("%sp%", chanceFormat.format(hasPremiumStatus ? (dynamicSpRate * Config.PREMIUM_RATE_SP) : dynamicExpRate));
			Float adenaChance = Config.RATE_DROP_CHANCE_BY_ID.get(Inventory.ADENA_ID);
			float dynamicAdenaChance = DynamicExpRateData.getInstance().getDynamicAdenaChance(activeChar.getLevel());
			Float adenaChancePremium = Config.PREMIUM_RATE_DROP_CHANCE_BY_ID.get(Inventory.ADENA_ID);
			float dynamicDropChance = DynamicExpRateData.getInstance().getDynamicDropChance(activeChar.getLevel());
			double chanceDropItem = Config.RATE_DEATH_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicDropChance(activeChar.getLevel());
			Float dropChancePremium = Config.PREMIUM_RATE_DROP_CHANCE;
			float dynamicSpoilChance = DynamicExpRateData.getInstance().getDynamicSpoilChance(activeChar.getLevel());
			double chanceSpoilItem = Config.RATE_SPOIL_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicSpoilChance(activeChar.getLevel());
			Float spoilChancePremium = Config.PREMIUM_RATE_SPOIL_CHANCE;
			html = html.replace("%adena_chance%", chanceFormat.format(hasPremiumStatus ? (adenaChancePremium * dynamicAdenaChance) : (adenaChance * dynamicAdenaChance)));
			html = html.replace("%drop_chance%", chanceFormat.format(hasPremiumStatus ? (dropChancePremium * dynamicDropChance) : (chanceDropItem * dynamicDropChance)));
			html = html.replace("%spoil_chance%", chanceFormat.format(hasPremiumStatus ? (spoilChancePremium * dynamicSpoilChance) : (chanceSpoilItem * dynamicSpoilChance)));
			html = html.replace("%rate_quest_drop%", chanceFormat.format(Config.RATE_QUEST_DROP));
		}
		else
		{
			html = html.replace("%xp%", chanceFormat.format(hasPremiumStatus ? Config.PREMIUM_RATE_XP : Config.RATE_XP));
			html = html.replace("%sp%", chanceFormat.format(hasPremiumStatus ? Config.PREMIUM_RATE_SP : Config.RATE_SP));
			Float adenaChance = Config.RATE_DROP_CHANCE_BY_ID.get(Inventory.ADENA_ID);
			float dynamicAdenaChance = DynamicExpRateData.getInstance().getDynamicAdenaChance(activeChar.getLevel());
			Float adenaChancePremium = Config.PREMIUM_RATE_DROP_CHANCE_BY_ID.get(Inventory.ADENA_ID);
			float dynamicDropChance = DynamicExpRateData.getInstance().getDynamicDropChance(activeChar.getLevel());
			double chanceDropItem = Config.RATE_DEATH_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicDropChance(activeChar.getLevel());
			Float dropChancePremium = Config.PREMIUM_RATE_DROP_CHANCE;
			float dynamicSpoilChance = DynamicExpRateData.getInstance().getDynamicSpoilChance(activeChar.getLevel());
			double chanceSpoilItem = Config.RATE_SPOIL_DROP_CHANCE_MULTIPLIER * DynamicExpRateData.getInstance().getDynamicSpoilChance(activeChar.getLevel());
			Float spoilChancePremium = Config.PREMIUM_RATE_SPOIL_CHANCE;
			html = html.replace("%adena_chance%", chanceFormat.format(hasPremiumStatus ? adenaChancePremium : adenaChance ));
			html = html.replace("%drop_chance%", chanceFormat.format(hasPremiumStatus ? dropChancePremium : chanceDropItem));
			html = html.replace("%spoil_chance%", chanceFormat.format(hasPremiumStatus ? spoilChancePremium : chanceSpoilItem));
			html = html.replace("%rate_quest_drop%", chanceFormat.format(Config.RATE_QUEST_DROP));
		}

		html = html.replace("%offline_play_time%", getOfflinePlayTimeLeft(activeChar));
		html = html.replace("%town_mode_status%", activeChar.getVariables().getBoolean(PlayerVariables.TOWN_MODE, false) ? "ONICON" : "OffICON");
		html = html.replace("%target_raid_mode_status%", activeChar.getVariables().getBoolean(PlayerVariables.TARGET_RAID, false) ? "ONICON" : "OffICON");
		html = html.replace("%show_range_mode_status%", activeChar.getVariables().getBoolean(PlayerVariables.SHOW_RANGE, false) ? "ONICON" : "OffICON");
		html = html.replace("%enable_heal_status%", activeChar.getVariables().getBoolean(PlayerVariables.ENABLE_HEAL, false) ? "ONICON" : "OffICON");
		html = html.replace("%enable_recharge_mp_status%", activeChar.getVariables().getBoolean(PlayerVariables.ENABLE_RECHARGE_MP, false) ? "ONICON" : "OffICON");
		html = html.replace("%assist_leader_mode_status%", activeChar.getVariables().getBoolean(PlayerVariables.ENABLE_ASSIT_PARTY_LEADER, false) ? "ONICON" : "OffICON");
		html = html.replace("%hp_percent_heal%", String.valueOf(activeChar.isEnableAssitPartyLeader() ? activeChar.getAutoPlaySettings().getHpThreshold() : "0"));
		html = html.replace("%mp_percent_heal%", String.valueOf(activeChar.isEnableAssitPartyLeader() ? activeChar.getAutoPlaySettings().getMpThreshold() : "0"));
		return html;
	}
}
