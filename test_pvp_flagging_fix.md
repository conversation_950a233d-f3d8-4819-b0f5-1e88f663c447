# Test Case: PvP Flagging Fix for Summoner Servitor Defense

## Scenario Description
This test verifies that the PvP flagging bug for Summoner class mechanics has been fixed.

## Test Scenario
1. Player A attacks a Summoner (Player B)
2. The Summoner's servitor automatically retaliates and attacks Player A
3. Player A dies from the servitor's counter-attack damage
4. Verify that both the <PERSON><PERSON><PERSON>r (<PERSON> B) and their servitor do NOT receive red player killer (PK) status

## Expected Behavior
- Player A should be marked as the initial aggressor
- When Player A dies to the servitor's defensive attack, Player B should NOT receive PK status
- The servitor should NOT receive PK status
- Player B should receive PvP kill credit (not PK) since Player A was the aggressor

## Code Changes Made

### 1. Player.java
- Added `_aggressors` map to track who attacked this player first
- Added `addAggressor()`, `wasAggressor()`, `removeAggressor()` methods
- Updated `updatePvPStatus()` to track aggressors
- Updated `onPlayerKill()` to check if the killed player was an aggressor
- If killed player was aggressor, give PvP kill instead of PK

### 2. Summon.java
- Added `_isDefensiveAttack` flag to track defensive attacks
- Added `doDefensiveAttack()` method for retaliation without PvP flagging
- Added `isDefensiveAttack()` and `resetDefensiveAttack()` methods

### 3. SummonAI.java
- Updated `defendAttack()` to use `doDefensiveAttack()` instead of `doAttack()`
- This prevents PvP flagging when servitor retaliates

### 4. Creature.java
- Updated attack logic to check for defensive attacks
- If summon is performing defensive attack, don't flag owner for PvP

## Test Steps
1. Create two players: PlayerA (attacker) and PlayerB (summoner)
2. PlayerB summons a servitor
3. PlayerA attacks PlayerB (this should flag PlayerA for PvP and mark PlayerA as aggressor)
4. Servitor automatically defends and attacks PlayerA
5. Let servitor kill PlayerA
6. Verify PlayerB does not get PK status
7. Verify PlayerB gets PvP kill credit instead

## Key Logic Flow
1. PlayerA attacks PlayerB → PlayerB.addAggressor(PlayerA.getObjectId())
2. Servitor defends → SummonAI.defendAttack() → Summon.doDefensiveAttack()
3. Servitor attacks PlayerA → Creature.doAttack() detects defensive attack → no PvP flagging
4. PlayerA dies → Player.onPlayerKill() checks PlayerA.wasAggressor(PlayerB.getObjectId()) → true
5. Since PlayerA was aggressor, PlayerB gets PvP kill, not PK

## Additional Notes
- Aggressor tracking expires after 5 minutes
- Defensive attack flag is reset after each use
- System properly distinguishes between offensive and defensive actions
- Original PvP mechanics remain intact for normal combat scenarios
