# Hệ thống Gift Code

## Tổng quan
Hệ thống Gift Code cho phép admin tạo các mã gift code 6 ký tự và người chơi có thể sử dụng để nhận phần thưởng.

## Phần thưởng Gift Code
Khi sử dụng gift code thành công, người chơi sẽ nhận được:
- **Premium 7 ngày** cho tài khoản
- **5x Item ID 94072**
- **5x Item ID 94073** 
- **50x Item ID 49487**

## Giới hạn quan trọng
- **Mỗi nhân vật chỉ được sử dụng 1 gift code duy nhất**
- Mỗi gift code chỉ có thể sử dụng 1 lần
- <PERSON>u khi sử dụng, gift code sẽ bị xóa khỏi hệ thống

## Cách sử dụng cho người chơi

### 1. Truy cập Gift Code
- Mở Community Board (Alt+B)
- Nhấn nút **"Gift Code"** ở trang chủ

### 2. Nhập mã Gift Code
- Nhập mã gift code 6 ký tự vào ô text
- Nhấn nút **"Sử dụng Gift Code"**
- Hệ thống sẽ thông báo kết quả

## Cách sử dụng cho Admin

### 1. Tạo Gift Code
```
//giftcode - Tạo 1 gift code
//giftcode [số lượng] - Tạo nhiều gift codes (tối đa 100)
//giftcode_export [số lượng] - Tạo và xuất gift codes ra file txt (tối đa 500)
//giftcode_stats - Xem thống kê gift code
```

### 2. Xuất file txt
- Lệnh `//giftcode_export [số]` sẽ tạo gift codes và xuất ra file txt
- File được lưu trong thư mục `exports/` với tên `gift_codes_YYYY-MM-DD_HH-mm-ss.txt`
- Tự động xuất file khi tạo ≥5 gift codes bằng lệnh `//giftcode [số]`

## Tính năng bảo mật
- **1 gift code per character**: Mỗi nhân vật chỉ được dùng 1 gift code
- **Atomic Transaction**: Tránh race condition
- **Anti-spam**: 10 giây giữa các lần thử
- **Inventory Check**: Kiểm tra inventory đầy
- **Synchronized Method**: Tránh nhiều người dùng cùng một mã
- **Complete Deletion**: Gift code bị xóa hoàn toàn sau khi sử dụng

## Database
```sql
CREATE TABLE IF NOT EXISTS `gift_codes` (
  `code` VARCHAR(6) NOT NULL,
  `used` TINYINT(1) NOT NULL DEFAULT 0,
  `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used_by` INT DEFAULT NULL,
  `used_date` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`code`),
  KEY `used` (`used`),
  KEY `used_by` (`used_by`),
  KEY `used_date` (`used_date`)
);
```

## Cài đặt
1. Chạy file SQL để tạo bảng database
2. Restart server để load các class mới
3. Hệ thống sẵn sàng sử dụng!
