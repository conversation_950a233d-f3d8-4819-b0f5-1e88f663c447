# Admin Test Commands for PvP Flagging Fix

## Cách test fix PvP flagging cho Summoner

### Setup Test Environment
1. Tạo 2 characters: <PERSON><PERSON> (attacker) và PlayerB (summoner)
2. PlayerB summon một servitor/pet
3. <PERSON><PERSON><PERSON> b<PERSON><PERSON> cả 2 players đều ở ngoài PvP zone

### Test Case 1: Servitor Retaliation
```
Bước 1: PlayerA attack PlayerB
- PlayerA sẽ bị PvP flag (tím)
- PlayerB.addAggressor(PlayerA.objectId) được gọi

Bước 2: Servitor tự động defend và attack PlayerA
- SummonAI.defendAttack() được gọi
- Summon.doDefensiveAttack() được gọi thay vì doAttack()
- PlayerB KHÔNG bị PvP flag

Bước 3: Servitor giết PlayerA
- Player.onPlayerKill() check wasAggressor() → true
- PlayerB gets PvP kill (không phải PK)
- PlayerB reputation không giảm
- PlayerB không bị red name

Expected: PlayerB chỉ tăng PvP kills, không tăng PK kills
```

### Test Case 2: Reflect Damage
```
Bước 1: Player<PERSON>'s summon có reflect damage skill/effect
- Có thể dùng skill hoặc item có reflect damage

Bước 2: PlayerA attack summon trực tiếp
- PlayerA bị PvP flag (tím)  
- Owner.addAggressor(PlayerA.objectId) được gọi

Bước 3: Reflect damage giết PlayerA
- Summon.doAttack() với reflect=true được gọi
- _reflectDamageKills.add(PlayerA.objectId)
- Player.onPlayerKill() check isReflectDamageKill() → true
- PlayerB gets PvP kill (không phải PK)

Expected: PlayerB chỉ tăng PvP kills, không tăng PK kills
```

### Debug Commands (for GM)
```
// Check aggressor status
//debug aggressor [playerName] - show who attacked this player

// Check reflect damage tracking  
//debug reflect [playerName] - show reflect damage targets

// Check PvP/PK stats
//debug pvp [playerName] - show PvP kills, PK kills, reputation

// Simulate aggressor
//debug addaggressor [targetPlayer] [attackerPlayer] - manually add aggressor

// Clear tracking
//debug clear [playerName] - clear aggressor and reflect tracking
```

### Manual Test Steps
1. **Setup**: 2 players, 1 summoner với servitor
2. **Test Normal PK**: PlayerA kills PlayerB → PlayerA gets PK ✓
3. **Test Defensive**: PlayerA attacks PlayerB → Servitor kills PlayerA → PlayerB gets PvP ✓  
4. **Test Reflect**: PlayerA attacks Summon → Reflect kills PlayerA → PlayerB gets PvP ✓

### Expected Behavior Changes
- **Before Fix**: Summoner luôn bị PK khi servitor/reflect giết người
- **After Fix**: Summoner chỉ bị PK khi chủ động tấn công trước, defensive kills = PvP

### Verification Points
1. Check PvP kills count (should increase)
2. Check PK kills count (should NOT increase for defensive)
3. Check reputation (should not decrease for defensive)
4. Check name color (should not turn red for defensive)
5. Check system messages (should show PvP kill, not PK)

### Common Issues to Check
- Aggressor tracking expires after 5 minutes (working as intended)
- Reflect damage tracking expires after 5 seconds (working as intended)  
- Multiple attackers: only original aggressor should be tracked
- Zone changes: aggressor tracking should persist
- Logout/login: tracking should be cleared

### Performance Notes
- Aggressor map auto-cleans old entries every 5 minutes
- Reflect damage set auto-cleans entries after 5 seconds
- Memory usage should be minimal
- No impact on normal PvP performance
